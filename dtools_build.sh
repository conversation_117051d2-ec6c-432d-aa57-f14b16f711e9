#!/bin/bash

output_dir=./outdir # 输出目录
tool_installpath=/usr/bin # dootls 存在目录
tool_env=aab118db # 123 环境 id
tool_app=previz_cms # 应用名
tool_server=frontend # 服务名
tool_user=henleygao # 发布人企业微信英文名
tool_instances=cls-203a634g-a2101324ee4fb6a2964a97a87783e477-20 # 23节点名（如果不指定，则为全量发布）

rm -rf ${outdir}
mkdir ${outdir}
deploy-node ./ ${outdir}/${tool_server}.tgz --exclude ${outdir} -D

#发布命令
${tool_installpath}/dtools dpatch -env ${tool_env} -app ${tool_app} -server ${tool_server} -tgz ${outdir}/${tool_server}.tgz  -user ${tool_user} -instances ${tool_instances}