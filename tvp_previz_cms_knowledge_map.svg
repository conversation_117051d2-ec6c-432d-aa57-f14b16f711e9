<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" viewBox="0 0 1400 1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色 -->
    <linearGradient id="problemGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffcdd2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ef5350;stop-opacity:0.8" />
    </linearGradient>
    <linearGradient id="solutionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#c8e6c9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#66bb6a;stop-opacity:0.8" />
    </linearGradient>
    <linearGradient id="techGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#bbdefb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#42a5f5;stop-opacity:0.8" />
    </linearGradient>
    <linearGradient id="businessGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e1bee7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ba68c8;stop-opacity:0.8" />
    </linearGradient>
    <linearGradient id="dataGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffe0b2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffb74d;stop-opacity:0.8" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="2" flood-color="#00000020"/>
    </filter>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
    <marker id="dashedArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#999" opacity="0.7" />
    </marker>
  </defs>

  <!-- 背景 -->
  <rect width="1400" height="1000" fill="#fafafa"/>
  
  <!-- 主标题 -->
  <text x="700" y="40" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="28" font-weight="bold" fill="#212121">
    预演工具内容管理平台 - 技术架构与解决方案
  </text>
  <text x="700" y="65" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="16" fill="#666">
    TVP Previz CMS - Technical Architecture &amp; Solutions
  </text>

  <!-- 层次背景区域 -->
  <!-- 用户价值层背景 -->
  <rect x="50" y="100" width="1300" height="160" rx="15" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="2" opacity="0.5"/>
  <text x="70" y="125" font-family="Microsoft YaHei, Arial, sans-serif" font-size="18" font-weight="bold" fill="#37474f">
    🎯 用户价值层 - 核心问题解决
  </text>

  <!-- 核心业务层背景 -->
  <rect x="50" y="280" width="1300" height="300" rx="15" fill="#f3e5f5" stroke="#e0e0e0" stroke-width="2" opacity="0.3"/>
  <text x="70" y="305" font-family="Microsoft YaHei, Arial, sans-serif" font-size="18" font-weight="bold" fill="#37474f">
    🏗️ 核心业务层 - CMS功能架构
  </text>

  <!-- 技术基础层背景 -->
  <rect x="50" y="600" width="1300" height="280" rx="15" fill="#e3f2fd" stroke="#e0e0e0" stroke-width="2" opacity="0.3"/>
  <text x="70" y="625" font-family="Microsoft YaHei, Arial, sans-serif" font-size="18" font-weight="bold" fill="#37474f">
    ⚡ 技术基础层 - 架构支撑
  </text>

  <!-- 用户价值层内容 -->
  <!-- 历史痛点 -->
  <rect x="100" y="150" width="280" height="100" rx="12" fill="url(#problemGradient)" stroke="#f44336" stroke-width="2" filter="url(#shadow)"/>
  <text x="240" y="175" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="16" font-weight="bold" fill="#c62828">🚫 历史痛点</text>
  <text x="110" y="195" font-family="Microsoft YaHei, Arial, sans-serif" font-size="12" fill="#d32f2f">• 功能与资产数据深度耦合</text>
  <text x="110" y="210" font-family="Microsoft YaHei, Arial, sans-serif" font-size="12" fill="#d32f2f">• 缺乏权限控制机制</text>
  <text x="110" y="225" font-family="Microsoft YaHei, Arial, sans-serif" font-size="12" fill="#d32f2f">• 存在数据泄露风险</text>
  <text x="110" y="240" font-family="Microsoft YaHei, Arial, sans-serif" font-size="12" fill="#d32f2f">• 开发与发布成本高</text>

  <!-- 解决方案 -->
  <rect x="1020" y="150" width="280" height="100" rx="12" fill="url(#solutionGradient)" stroke="#4caf50" stroke-width="2" filter="url(#shadow)"/>
  <text x="1160" y="175" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="16" font-weight="bold" fill="#2e7d32">✅ 解决方案</text>
  <text x="1030" y="195" font-family="Microsoft YaHei, Arial, sans-serif" font-size="12" fill="#388e3c">• 功能与资产完全解耦</text>
  <text x="1030" y="210" font-family="Microsoft YaHei, Arial, sans-serif" font-size="12" fill="#388e3c">• 项目级权限隔离控制</text>
  <text x="1030" y="225" font-family="Microsoft YaHei, Arial, sans-serif" font-size="12" fill="#388e3c">• 多版本协作管理</text>
  <text x="1030" y="240" font-family="Microsoft YaHei, Arial, sans-serif" font-size="12" fill="#388e3c">• 第三方API集成能力</text>

  <!-- 核心业务层内容 -->
  <!-- 项目管理 -->
  <rect x="100" y="330" width="220" height="120" rx="10" fill="url(#businessGradient)" stroke="#9c27b0" stroke-width="2" filter="url(#shadow)"/>
  <text x="210" y="355" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="14" font-weight="bold" fill="#6a1b9a">📁 项目管理</text>
  <text x="210" y="375" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="12" fill="#7b1fa2">Project Management</text>
  <text x="110" y="395" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#7b1fa2">• 项目创建与编辑</text>
  <text x="110" y="410" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#7b1fa2">• 权限分配管理</text>
  <text x="110" y="425" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#7b1fa2">• 状态流程控制</text>
  <text x="110" y="440" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#7b1fa2">• 协作共享机制</text>

  <!-- 资产管理 -->
  <rect x="350" y="330" width="220" height="120" rx="10" fill="url(#businessGradient)" stroke="#9c27b0" stroke-width="2" filter="url(#shadow)"/>
  <text x="460" y="355" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="14" font-weight="bold" fill="#6a1b9a">🎨 资产管理</text>
  <text x="460" y="375" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="12" fill="#7b1fa2">Asset Management</text>
  <text x="360" y="395" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#7b1fa2">• 多版本控制系统</text>
  <text x="360" y="410" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#7b1fa2">• 元数据存储检索</text>
  <text x="360" y="425" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#7b1fa2">• 增删改查操作</text>
  <text x="360" y="440" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#7b1fa2">• 文件上传下载</text>

  <!-- 场景管理 -->
  <rect x="600" y="330" width="220" height="120" rx="10" fill="url(#businessGradient)" stroke="#9c27b0" stroke-width="2" filter="url(#shadow)"/>
  <text x="710" y="355" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="14" font-weight="bold" fill="#6a1b9a">🎬 场景管理</text>
  <text x="710" y="375" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="12" fill="#7b1fa2">Scene Management</text>
  <text x="610" y="395" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#7b1fa2">• 场景编排设计</text>
  <text x="610" y="410" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#7b1fa2">• 序列管理系统</text>
  <text x="610" y="425" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#7b1fa2">• 故事板制作</text>
  <text x="610" y="440" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#7b1fa2">• UE5引擎集成</text>

  <!-- 数据管理 -->
  <rect x="850" y="330" width="220" height="120" rx="10" fill="url(#businessGradient)" stroke="#9c27b0" stroke-width="2" filter="url(#shadow)"/>
  <text x="960" y="355" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="14" font-weight="bold" fill="#6a1b9a">💬 数据管理</text>
  <text x="960" y="375" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="12" fill="#7b1fa2">Data Management</text>
  <text x="860" y="395" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#7b1fa2">• 对话数据处理</text>
  <text x="860" y="410" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#7b1fa2">• 对话组管理</text>
  <text x="860" y="425" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#7b1fa2">• 剧本拆解分析</text>
  <text x="860" y="440" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#7b1fa2">• 第三方API调用</text>

  <!-- 权限管理中心 -->
  <ellipse cx="1160" cy="390" rx="80" ry="40" fill="url(#dataGradient)" stroke="#ff9800" stroke-width="2" filter="url(#shadow)"/>
  <text x="1160" y="385" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="12" font-weight="bold" fill="#ef6c00">🔐 权限中心</text>
  <text x="1160" y="400" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="10" fill="#ef6c00">Permission Hub</text>

  <!-- 技术基础层内容 -->
  <!-- 前端架构 -->
  <rect x="100" y="650" width="200" height="100" rx="10" fill="url(#techGradient)" stroke="#2196f3" stroke-width="2" filter="url(#shadow)"/>
  <text x="200" y="675" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="14" font-weight="bold" fill="#1565c0">🖥️ 前端架构</text>
  <text x="200" y="695" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="12" fill="#1976d2">React + TypeScript</text>
  <text x="110" y="715" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#1976d2">• TDesign UI组件</text>
  <text x="110" y="730" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#1976d2">• Redux状态管理</text>
  <text x="110" y="745" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#1976d2">• React Router路由</text>

  <!-- 认证体系 -->
  <rect x="320" y="650" width="200" height="100" rx="10" fill="url(#techGradient)" stroke="#2196f3" stroke-width="2" filter="url(#shadow)"/>
  <text x="420" y="675" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="14" font-weight="bold" fill="#1565c0">🔐 认证体系</text>
  <text x="420" y="695" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="12" fill="#1976d2">腾讯TVI OAuth</text>
  <text x="330" y="715" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#1976d2">• 网关鉴权机制</text>
  <text x="330" y="730" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#1976d2">• JWT Token管理</text>
  <text x="330" y="745" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#1976d2">• 权限校验守卫</text>

  <!-- API架构 -->
  <rect x="540" y="650" width="200" height="100" rx="10" fill="url(#techGradient)" stroke="#2196f3" stroke-width="2" filter="url(#shadow)"/>
  <text x="640" y="675" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="14" font-weight="bold" fill="#1565c0">🔗 API架构</text>
  <text x="640" y="695" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="12" fill="#1976d2">REST + TRPC</text>
  <text x="550" y="715" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#1976d2">• 第三方API集成</text>
  <text x="550" y="730" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#1976d2">• 数据传输协议</text>
  <text x="550" y="745" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#1976d2">• 服务间通信</text>

  <!-- 数据存储 -->
  <rect x="760" y="650" width="200" height="100" rx="10" fill="url(#techGradient)" stroke="#2196f3" stroke-width="2" filter="url(#shadow)"/>
  <text x="860" y="675" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="14" font-weight="bold" fill="#1565c0">💾 数据存储</text>
  <text x="860" y="695" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="12" fill="#1976d2">多版本管理</text>
  <text x="770" y="715" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#1976d2">• 文件上传处理</text>
  <text x="770" y="730" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#1976d2">• 元数据存储</text>
  <text x="770" y="745" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#1976d2">• 版本控制系统</text>

  <!-- 开发工具链 -->
  <rect x="980" y="650" width="200" height="100" rx="10" fill="url(#techGradient)" stroke="#2196f3" stroke-width="2" filter="url(#shadow)"/>
  <text x="1080" y="675" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="14" font-weight="bold" fill="#1565c0">🛠️ 开发工具</text>
  <text x="1080" y="695" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="12" fill="#1976d2">Vite + ESLint</text>
  <text x="990" y="715" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#1976d2">• 快速构建打包</text>
  <text x="990" y="730" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#1976d2">• 代码质量检查</text>
  <text x="990" y="745" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#1976d2">• 热重载调试</text>

  <!-- 核心特性标注 -->
  <rect x="1100" y="480" width="250" height="100" rx="10" fill="url(#dataGradient)" stroke="#ff9800" stroke-width="2" filter="url(#shadow)"/>
  <text x="1225" y="505" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="14" font-weight="bold" fill="#ef6c00">🌟 核心特性</text>
  <text x="1110" y="525" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#ef6c00">🛡️ 项目间数据隔离保护</text>
  <text x="1110" y="540" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#ef6c00">🔄 多版本协作开发支持</text>
  <text x="1110" y="555" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#ef6c00">🌐 第三方API集成能力</text>
  <text x="1110" y="570" font-family="Microsoft YaHei, Arial, sans-serif" font-size="11" fill="#ef6c00">📊 元数据资产智能搜索</text>

  <!-- 连接线和数据流 -->
  <!-- 问题到解决方案的关系 -->
  <path d="M 380 200 Q 700 180 1020 200" stroke="#666" stroke-width="2" stroke-dasharray="5,5" fill="none" marker-end="url(#dashedArrow)"/>
  <text x="700" y="175" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="12" fill="#666">解决</text>

  <!-- 解决方案到业务层的实现 -->
  <line x1="1160" y1="250" x2="210" y2="330" stroke="#4caf50" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="1160" y1="250" x2="460" y2="330" stroke="#4caf50" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="1160" y1="250" x2="710" y2="330" stroke="#4caf50" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="1160" y1="250" x2="960" y2="330" stroke="#4caf50" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- 权限中心到各业务模块的控制 -->
  <path d="M 1080 390 Q 1000 360 320 390" stroke="#ff9800" stroke-width="2" stroke-dasharray="3,3" fill="none" marker-end="url(#dashedArrow)"/>
  <path d="M 1080 390 Q 950 360 570 390" stroke="#ff9800" stroke-width="2" stroke-dasharray="3,3" fill="none" marker-end="url(#dashedArrow)"/>
  <path d="M 1080 390 Q 900 360 820 390" stroke="#ff9800" stroke-width="2" stroke-dasharray="3,3" fill="none" marker-end="url(#dashedArrow)"/>

  <!-- 业务层到技术层的依赖 -->
  <line x1="200" y1="450" x2="200" y2="650" stroke="#9c27b0" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="210" y1="450" x2="420" y2="650" stroke="#9c27b0" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="460" y1="450" x2="860" y2="650" stroke="#9c27b0" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="710" y1="450" x2="640" y2="650" stroke="#9c27b0" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="960" y1="450" x2="640" y2="650" stroke="#9c27b0" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- 技术栈标签 -->
  <rect x="1200" y="800" width="150" height="80" rx="8" fill="#fff" stroke="#e0e0e0" stroke-width="1" filter="url(#shadow)"/>
  <text x="1275" y="820" text-anchor="middle" font-family="Microsoft YaHei, Arial, sans-serif" font-size="12" font-weight="bold" fill="#37474f">技术栈标签</text>
  <circle cx="1220" cy="835" r="6" fill="#2196f3"/>
  <text x="1235" y="840" font-family="Microsoft YaHei, Arial, sans-serif" font-size="10" fill="#666">技术基础</text>
  <circle cx="1220" cy="850" r="6" fill="#9c27b0"/>
  <text x="1235" y="855" font-family="Microsoft YaHei, Arial, sans-serif" font-size="10" fill="#666">业务功能</text>
  <circle cx="1220" cy="865" r="6" fill="#ff9800"/>
  <text x="1235" y="870" font-family="Microsoft YaHei, Arial, sans-serif" font-size="10" fill="#666">核心特性</text>

  <!-- 版权信息 -->
  <text x="70" y="950" font-family="Microsoft YaHei, Arial, sans-serif" font-size="10" fill="#999">
    © 2024 预演工具内容管理平台 - 基于腾讯视频技术中心架构设计
  </text>
  <text x="70" y="970" font-family="Microsoft YaHei, Arial, sans-serif" font-size="10" fill="#999">
    技术栈：React + TypeScript + TDesign + Redux + TRPC + 腾讯TVI OAuth
  </text>
</svg> 