import { useEffect, useCallback, useMemo, useRef, useState } from 'react';
import { RenderOptimizer, TaskScheduler, MemoryOptimizer, PerformanceReporter } from '../utils/performanceOptimizer';

/**
 * 性能优化 Hook
 * 提供常用的性能优化功能
 */

// 防抖 Hook
export function useDebounce<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): (...args: Parameters<T>) => void {
  return useMemo(
    () => RenderOptimizer.debounce(callback, delay),
    [callback, delay]
  );
}

// 节流 Hook
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): (...args: Parameters<T>) => void {
  return useMemo(
    () => RenderOptimizer.throttle(callback, delay),
    [callback, delay]
  );
}

// 大数据处理 Hook
export function useLargeDataProcessor() {
  const taskScheduler = useRef(new TaskScheduler());

  const processLargeDataset = useCallback(
    async <T>(
      data: T[],
      processor: (item: T) => void,
      chunkSize: number = 100
    ) => {
      return TaskScheduler.processLargeDataset(data, processor, chunkSize);
    },
    []
  );

  const scheduleTask = useCallback((task: () => void) => {
    taskScheduler.current.scheduleTask(task);
  }, []);

  return { processLargeDataset, scheduleTask };
}

// 内存监控 Hook
export function useMemoryMonitor(interval: number = 5000) {
  const intervalRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    const monitor = () => {
      const memoryUsage = MemoryOptimizer.getMemoryUsage();
      if (memoryUsage) {
        // 如果内存使用超过 80%，清理缓存
        const usagePercent = memoryUsage.used / memoryUsage.total;
        if (usagePercent > 0.8) {
          console.warn('内存使用率过高，清理缓存:', memoryUsage);
          MemoryOptimizer.clearCache();
        }
      }
    };

    intervalRef.current = setInterval(monitor, interval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [interval]);

  return {
    getMemoryUsage: MemoryOptimizer.getMemoryUsage,
    clearCache: MemoryOptimizer.clearCache,
  };
}

// 长任务监控 Hook
export function useLongTaskMonitor() {
  useEffect(() => {
    const observer = PerformanceReporter.observeLongTasks((entry) => {
      console.warn('检测到长任务:', {
        name: entry.name,
        duration: entry.duration,
        startTime: entry.startTime,
      });
      
      // 如果长任务超过 100ms，记录到性能监控
      if (entry.duration > 100) {
        console.error('严重的长任务阻塞:', entry);
      }
    });

    return () => {
      observer?.disconnect();
    };
  }, []);
}

// 虚拟滚动 Hook
export function useVirtualScroll(
  itemCount: number,
  itemHeight: number,
  containerHeight: number
) {
  const [scrollTop, setScrollTop] = useState(0);

  const visibleRange = useMemo(() => {
    return RenderOptimizer.calculateVisibleItems(
      containerHeight,
      itemHeight,
      scrollTop,
      itemCount
    );
  }, [containerHeight, itemHeight, scrollTop, itemCount]);

  const handleScroll = useCallback(
    RenderOptimizer.throttle((scrollTop: number) => {
      setScrollTop(scrollTop);
    }, 16),
    []
  );

  return {
    visibleRange,
    handleScroll,
    totalHeight: itemCount * itemHeight,
    offsetY: visibleRange.startIndex * itemHeight,
  };
}

// 性能报告 Hook
export function usePerformanceReport() {
  const generateReport = useCallback(() => {
    return PerformanceReporter.generatePerformanceReport();
  }, []);

  const logPerformanceReport = useCallback(() => {
    const report = generateReport();
    console.group('📊 性能报告');
    console.log('页面加载时间:', `${report.pageLoadTime.toFixed(2)}ms`);
    console.log('DOM内容加载时间:', `${report.domContentLoaded.toFixed(2)}ms`);
    console.log('首次内容绘制:', `${report.firstContentfulPaint.toFixed(2)}ms`);
    console.log('内存使用:', report.memoryUsage);
    console.log('资源统计:', report.resourceStats);
    console.groupEnd();
    return report;
  }, [generateReport]);

  return { generateReport, logPerformanceReport };
}

// 组件渲染性能监控 Hook
export function useRenderPerformance(componentName: string) {
  const renderCount = useRef(0);
  const lastRenderTime = useRef(Date.now());

  useEffect(() => {
    renderCount.current += 1;
    const now = Date.now();
    const timeSinceLastRender = now - lastRenderTime.current;
    lastRenderTime.current = now;

    // 如果渲染频率过高（小于 16ms），发出警告
    if (timeSinceLastRender < 16 && renderCount.current > 1) {
      console.warn(`组件 ${componentName} 渲染频率过高:`, {
        renderCount: renderCount.current,
        timeSinceLastRender,
      });
    }

    // 开发环境下记录渲染信息
    if (import.meta.env.MODE === 'development') {
      console.log(`🔄 组件渲染 [${componentName}]:`, {
        count: renderCount.current,
        interval: timeSinceLastRender,
      });
    }
  });

  return {
    renderCount: renderCount.current,
  };
}

// 网络请求优化 Hook
export function useOptimizedRequest() {
  const requestCache = useRef(new Map<string, Promise<any>>());

  const deduplicatedRequest = useCallback(
    async <T>(key: string, requestFn: () => Promise<T>): Promise<T> => {
      if (requestCache.current.has(key)) {
        return requestCache.current.get(key);
      }

      const promise = requestFn().finally(() => {
        requestCache.current.delete(key);
      });

      requestCache.current.set(key, promise);
      return promise;
    },
    []
  );

  const clearRequestCache = useCallback(() => {
    requestCache.current.clear();
  }, []);

  return { deduplicatedRequest, clearRequestCache };
}

// 综合性能优化 Hook
export function usePerformanceOptimization(options: {
  enableMemoryMonitor?: boolean;
  enableLongTaskMonitor?: boolean;
  memoryMonitorInterval?: number;
  componentName?: string;
}) {
  const {
    enableMemoryMonitor = true,
    enableLongTaskMonitor = true,
    memoryMonitorInterval = 5000,
    componentName = 'Unknown',
  } = options;

  // 启用内存监控
  const memoryMonitor = useMemoryMonitor(
    enableMemoryMonitor ? memoryMonitorInterval : 0
  );

  // 启用长任务监控
  if (enableLongTaskMonitor) {
    useLongTaskMonitor();
  }

  // 启用渲染性能监控
  const renderPerformance = useRenderPerformance(componentName);

  // 提供工具函数
  const { processLargeDataset, scheduleTask } = useLargeDataProcessor();
  const { generateReport, logPerformanceReport } = usePerformanceReport();
  const { deduplicatedRequest } = useOptimizedRequest();

  return {
    // 内存相关
    ...memoryMonitor,
    
    // 渲染相关
    renderCount: renderPerformance.renderCount,
    
    // 任务处理
    processLargeDataset,
    scheduleTask,
    
    // 网络请求
    deduplicatedRequest,
    
    // 性能报告
    generateReport,
    logPerformanceReport,
    
    // 工具函数
    debounce: useDebounce,
    throttle: useThrottle,
  };
}
