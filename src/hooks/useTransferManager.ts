import { useState, useCallback, useEffect, useRef } from 'react';
import { NotificationPlugin, MessagePlugin } from 'tdesign-react';
import { TransferTask } from '../components/TransferManager';
import { instance, getCurrentAuth } from '../utils/request';
import { markUploadTaskFailed } from '../services/assetUpload';

// 并发控制配置
const MAX_CONCURRENT_UPLOADS = 10; // 最大并发上传数量

// 传输控制器管理
interface TransferController {
  taskId: string;
  abortController: AbortController;
  type: 'upload' | 'download';
  resumeData?: any; // 用于存储断点续传数据
}

export const useTransferManager = () => {
  // 任务状态持久化的key
  const TASKS_STORAGE_KEY = 'transfer_manager_tasks';

  const [tasks, setTasks] = useState<TransferTask[]>([]);
  const [isVisible, setIsVisible] = useState(false); // 控制面板可见性（非最小化）
  const [controllers, setControllers] = useState<Map<string, TransferController>>(new Map());

  // 保存恢复上传的回调函数
  const resumeUploadCallbackRef = useRef<((taskId: string, resumeFromChunk: number, assetId: string, assetName: string) => void) | null>(null);

  // 保存任务到localStorage
  const saveTasks = useCallback((tasksToSave: TransferTask[]) => {
    try {
      // 只保存需要持久化的任务（暂停的、失败的、运行中的）
      const tasksToStore = tasksToSave.filter(task =>
        task.status === 'paused' ||
        task.status === 'failed' ||
        task.status === 'running'
      );
      localStorage.setItem(TASKS_STORAGE_KEY, JSON.stringify(tasksToStore));
    } catch (error) {
      console.warn('保存任务状态失败:', error);
    }
  }, [TASKS_STORAGE_KEY]);

  // 从localStorage恢复任务
  const loadTasks = useCallback(() => {
    try {
      const savedTasks = localStorage.getItem(TASKS_STORAGE_KEY);
      if (savedTasks) {
        const parsedTasks: TransferTask[] = JSON.parse(savedTasks);
        // 恢复日期对象
        const restoredTasks = parsedTasks.map(task => ({
          ...task,
          createdAt: new Date(task.createdAt),
          completedAt: task.completedAt ? new Date(task.completedAt) : undefined,
          // 将运行中的任务状态改为暂停，避免状态不一致
          status: task.status === 'running' ? 'paused' as const : task.status
        }));
        setTasks(restoredTasks);
        console.log('已恢复任务状态:', restoredTasks.length, '个任务');
      }
    } catch (error) {
      console.warn('恢复任务状态失败:', error);
    }
  }, [TASKS_STORAGE_KEY]);

  // 页面加载时恢复任务状态
  useEffect(() => {
    loadTasks();
  }, [loadTasks]);

  // 任务状态变化时自动保存
  useEffect(() => {
    if (tasks.length > 0) {
      saveTasks(tasks);
    }
  }, [tasks, saveTasks]);

  // 添加任务
  const addTask = useCallback((task: Omit<TransferTask, 'id' | 'createdAt'>) => {
    const newTask: TransferTask = {
      ...task,
      id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
      createdAt: new Date(),
    };
    
    setTasks(prev => [newTask, ...prev]);
    setIsVisible(true);
    
    // 显示通知
    NotificationPlugin.info({
      title: '任务已添加',
      content: `${task.type === 'upload' ? '上传' : '下载'}任务 "${task.name}" 已添加到传输队列`,
      duration: 3000,
    });
    
    return newTask.id;
  }, []);

  // 更新任务状态
  const updateTask = useCallback((taskId: string, updates: Partial<TransferTask>) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId ? { ...task, ...updates } : task
    ));
    
    // 如果任务完成或失败，显示通知
    if (updates.status === 'completed') {
      const task = tasks.find(t => t.id === taskId);
      if (task) {
        NotificationPlugin.success({
          title: '传输完成',
          content: `${task.type === 'upload' ? '上传' : '下载'}任务 "${task.name}" 已完成`,
          duration: 3000,
        });
      }
    } else if (updates.status === 'failed') {
      const task = tasks.find(t => t.id === taskId);
      if (task) {
        NotificationPlugin.error({
          title: '传输失败',
          content: `${task.type === 'upload' ? '上传' : '下载'}任务 "${task.name}" 失败: ${updates.error || '未知错误'}`,
          duration: 5000,
        });
      }
    }
  }, [tasks]);

  // 添加传输控制器
  const addController = useCallback((taskId: string, abortController: AbortController, type: 'upload' | 'download', resumeData?: any) => {
    setControllers(prev => {
      const newMap = new Map(prev);
      newMap.set(taskId, { taskId, abortController, type, resumeData });
      return newMap;
    });
  }, []);

  // 移除传输控制器
  const removeController = useCallback((taskId: string) => {
    setControllers(prev => {
      const newMap = new Map(prev);
      const controller = newMap.get(taskId);
      if (controller) {
        controller.abortController.abort();
      }
      newMap.delete(taskId);
      return newMap;
    });
  }, []);

  // 保存断点续传数据到localStorage
  const saveResumeData = useCallback((taskId: string, data: any) => {
    try {
      const key = `transfer_resume_${taskId}`;
      localStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to save resume data:', error);
    }
  }, []);

  // 从localStorage读取断点续传数据
  const loadResumeData = useCallback((taskId: string) => {
    try {
      const key = `transfer_resume_${taskId}`;
      const data = localStorage.getItem(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.warn('Failed to load resume data:', error);
      return null;
    }
  }, []);

  // 清除断点续传数据
  const clearResumeData = useCallback((taskId: string) => {
    try {
      const key = `transfer_resume_${taskId}`;
      localStorage.removeItem(key);
      // 同时清除上传断点续传数据
      const uploadKey = `upload_resume_${taskId}`;
      localStorage.removeItem(uploadKey);
    } catch (error) {
      console.warn('Failed to clear resume data:', error);
    }
  }, []);



  // 重试上传任务
  const retryUploadTask = useCallback(async (task: TransferTask) => {
    if (!task.assetId) {
      MessagePlugin.error(`任务 "${task.name}" 缺少资产ID，无法重试`);
      return;
    }

    try {
      // 1. 先标记之前的任务为失败
      console.log(`[重试上传] 开始重试任务: ${task.name}, assetId: ${task.assetId}, jobId: ${task.jobId}`);

      if (task.jobId) {
        console.log(`[重试上传] 发送失败通知, jobId: ${task.jobId}, totalChunks: ${task.totalChunks}`);
        await markUploadTaskFailed(task.assetId, task.jobId, task.totalChunks, 'User retry - marking previous task as failed');
      } else {
        console.log(`[重试上传] 发送失败通知, 无jobId, totalChunks: ${task.totalChunks}`);
        await markUploadTaskFailed(task.assetId, undefined, task.totalChunks, 'User retry - marking previous task as failed');
      }

      // 2. 重置任务状态
      updateTask(task.id, {
        status: 'pending',
        progress: 0,
        transferredSize: 0,
        error: undefined,
        jobId: undefined, // 清除旧的jobId
        totalChunks: undefined // 清除旧的分片数量，重新计算
      });

      // 3. 清除断点续传数据
      clearResumeData(task.id);

      // 4. 自动打开文件选择对话框（类似断点续传恢复机制）
      if (resumeUploadCallbackRef.current && task.assetId) {
        console.log(`[重试上传] 自动打开文件选择对话框, taskId: ${task.id}, assetId: ${task.assetId}`);
        // 使用 resumeFromChunk = 0 表示这是重试（从头开始），而不是断点续传
        resumeUploadCallbackRef.current(task.id, 0, task.assetId, task.name);
        MessagePlugin.success(`任务 "${task.name}" 重试已开始，请选择文件重新上传`);
      } else {
        // 如果没有回调函数，则使用原来的提示方式
        MessagePlugin.success(`任务 "${task.name}" 重试已开始，请重新选择文件并开始上传`);
      }

    } catch (error) {
      MessagePlugin.error(`重试任务 "${task.name}" 失败: ${(error as any)?.message || '未知错误'}`);
      console.error('Retry upload task failed:', error);
    }
  }, [updateTask, clearResumeData]);

  // 获取断点续传信息
  const getResumeInfo = useCallback((taskId: string) => {
    try {
      const resumeKey = `upload_resume_${taskId}`;
      const resumeDataStr = localStorage.getItem(resumeKey);

      if (!resumeDataStr) {
        return null;
      }

      const resumeData = JSON.parse(resumeDataStr);
      return {
        resumeFromChunk: (resumeData.currentChunk || 1) + 1,
        resumeData
      };
    } catch (error) {
      console.warn('获取断点续传信息失败:', error);
      return null;
    }
  }, []);

  // 触发恢复上传的文件选择
  const triggerResumeUpload = useCallback((taskId: string, onResumeCallback?: (taskId: string, resumeFromChunk: number, assetId: string, assetName: string) => void) => {
    const task = tasks.find(t => t.id === taskId);
    if (!task || task.type !== 'upload') {
      MessagePlugin.error('任务不存在或不是上传任务');
      return;
    }

    const resumeInfo = getResumeInfo(taskId);
    if (!resumeInfo) {
      MessagePlugin.error('缺少断点续传数据，请重试上传');
      return;
    }

    // 调用回调函数，让页面组件处理文件选择
    if (onResumeCallback && task.assetId) {
      onResumeCallback(taskId, resumeInfo.resumeFromChunk, task.assetId, task.name);
    } else {
      MessagePlugin.error('无法恢复上传：缺少回调函数或资产ID');
    }
  }, [tasks, getResumeInfo]);

  // 设置恢复上传回调函数
  const setResumeUploadCallback = useCallback((callback: (taskId: string, resumeFromChunk: number, assetId: string, assetName: string) => void) => {
    resumeUploadCallbackRef.current = callback;
  }, []);

  // 恢复暂停的上传任务
  const resumeUploadTask = useCallback(async (task: TransferTask, onResumeCallback?: (taskId: string, resumeFromChunk: number, assetId: string, assetName: string) => void) => {
    if (!task.assetId || !task.jobId) {
      MessagePlugin.error(`任务 "${task.name}" 缺少必要信息，无法恢复`);
      return;
    }

    // 触发文件选择流程，传递回调函数
    triggerResumeUpload(task.id, onResumeCallback);
  }, [triggerResumeUpload]);

  // 实际下载文件（支持断点续传）
  const startActualDownload = useCallback(async (task: TransferTask, resumeFromPause = false) => {
    if (!task.belvethFid) return;

    try {
      // 创建AbortController
      const abortController = new AbortController();
      addController(task.id, abortController, 'download');

      // 检查是否有断点续传数据
      let resumeData = null;
      if (resumeFromPause) {
        resumeData = loadResumeData(task.id);
      }

      updateTask(task.id, {
        status: 'running',
        progress: resumeData?.progress || 0,
        transferredSize: resumeData?.transferredSize || 0
      });

      // 使用fetch进行下载，参考FileDownloader的实现
      const baseRaw = ((instance.defaults as any)?.baseURL || '').replace(/\/$/, '');
      const fullUrl = `${baseRaw}/download`;
      let urlWithQuery = `${fullUrl}?belveth_fid=${encodeURIComponent(task.belvethFid)}`;

      // 如果有完整文件名，添加name参数
      if (task.belvethFilename) {
        urlWithQuery += `&name=${encodeURIComponent(task.belvethFilename)}`;
      }

      // 设置下载请求头（支持断点续传）
      const headers: Record<string, string> = {};
      const token = getCurrentAuth();
      if (token) headers.Authorization = `Bearer ${token}`;
      if (resumeData && resumeData.transferredSize > 0) {
        headers['Range'] = `bytes=${resumeData.transferredSize}-`;
      }

      const startTime = Date.now();
      const response = await fetch(urlWithQuery, {
        method: 'GET',
        credentials: 'include',
        headers,
        signal: abortController.signal,
      });

      if (!response.ok) {
        if (response.status === 401) throw new Error('未授权（401），请先登录后重试');
        throw new Error(`下载失败: ${response.status}`);
      }

      // 获取文件大小
      const contentLength = response.headers.get('content-length');
      const fileSize = contentLength ? parseInt(contentLength, 10) + (resumeData?.transferredSize || 0) : 0;

      if (fileSize > 0) {
        updateTask(task.id, { size: fileSize });
      }

      // 读取响应流并跟踪进度
      const reader = response.body?.getReader();
      if (!reader) throw new Error('无法读取响应流');

      const chunks: Uint8Array[] = [];
      let receivedLength = 0;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        chunks.push(value);
        receivedLength += value.length;

        const currentTransferred = (resumeData?.transferredSize || 0) + receivedLength;
        const progress = fileSize > 0 ? Math.round((currentTransferred / fileSize) * 100) : 0;
        const speed = receivedLength / ((Date.now() - startTime) / 1000);

        // 保存断点续传数据
        const resumeInfo = {
          progress,
          transferredSize: currentTransferred,
          fileSize,
          timestamp: Date.now()
        };
        saveResumeData(task.id, resumeInfo);

        updateTask(task.id, {
          progress,
          transferredSize: currentTransferred,
          size: fileSize,
          speed,
        });
      }

      // 合并所有数据块
      const allChunks = new Uint8Array(receivedLength);
      let position = 0;
      for (const chunk of chunks) {
        allChunks.set(chunk, position);
        position += chunk.length;
      }

      // 创建Blob对象
      const blob = new Blob([allChunks]);

      // 从响应头获取文件名
      const cd = response.headers.get('content-disposition') || '';
      let filename = task.name;
      const match1 = /filename\*=UTF-8''([^;]+)/i.exec(cd);
      const match2 = /filename="?([^";]+)"?/i.exec(cd);
      if (match1) filename = decodeURIComponent(match1[1]);
      else if (match2) filename = match2[1];
      if (!filename) filename = `download_${task.belvethFid}`;

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      // 清理
      removeController(task.id);
      clearResumeData(task.id);

      updateTask(task.id, {
        status: 'completed',
        progress: 100,
        completedAt: new Date(),
        speed: undefined
      });

      MessagePlugin.success(`文件 "${task.name}" 下载完成`);

    } catch (error) {
      if ((error as any)?.name === 'AbortError') {
        // 用户主动取消，保存当前进度
        updateTask(task.id, { status: 'paused' });
        return;
      }

      const errorMessage = (error as any)?.message || '下载失败';
      removeController(task.id);

      updateTask(task.id, {
        status: 'failed',
        error: errorMessage
      });

      MessagePlugin.error(`文件 "${task.name}" 下载失败: ${errorMessage}`);
    }
  }, [updateTask, addController, removeController, saveResumeData, loadResumeData, clearResumeData]);

  // 任务操作
  const handleTaskAction = useCallback((taskId: string, action: 'pause' | 'resume' | 'retry' | 'cancel') => {
    const task = tasks.find(t => t.id === taskId);
    if (!task) return;

    switch (action) {
      case 'pause':
        // 真正暂停传输
        const controller = controllers.get(taskId);
        if (controller) {
          controller.abortController.abort();
          // 更新任务状态为暂停
          updateTask(taskId, { status: 'paused' });
          // 不立即移除控制器，保留用于恢复时使用
        } else {
          // 如果没有控制器，直接更新状态（可能是还未开始上传的任务）
          updateTask(taskId, { status: 'paused' });
        }
        MessagePlugin.info(`任务 "${task.name}" 已暂停`);
        break;

      case 'resume':
        // 恢复传输
        if (task.type === 'download' && task.belvethFid) {
          startActualDownload(task, true); // 传入true表示从暂停恢复
        } else if (task.type === 'upload') {
          // 上传任务恢复使用断点续传，传递回调函数
          resumeUploadTask(task, resumeUploadCallbackRef.current || undefined);
        }
        break;

      case 'retry':
        // 重试任务
        if (task.type === 'download' && task.belvethFid) {
          // 下载任务重试
          clearResumeData(taskId); // 清除断点续传数据，从头开始
          updateTask(taskId, { status: 'pending', progress: 0, transferredSize: 0, error: undefined });
          startActualDownload(task, false); // 从头开始下载
        } else if (task.type === 'upload') {
          // 上传任务重试
          retryUploadTask(task);
        }
        break;

      case 'cancel':
        // 取消任务
        const cancelController = controllers.get(taskId);
        if (cancelController) {
          cancelController.abortController.abort();
          removeController(taskId);
        }

        // 如果是上传任务且有jobId，调用markUploadTaskFailed通知服务端
        if (task.type === 'upload' && task.jobId && task.assetId) {
          markUploadTaskFailed(task.assetId, task.jobId, task.totalChunks, '用户取消上传')
            .then(() => {
              console.log(`[取消上传] 已通知服务端任务失败: ${task.name}`);
            })
            .catch((error) => {
              console.warn(`[取消上传] 通知服务端失败:`, error);
            });
        }

        clearResumeData(taskId); // 清除断点续传数据
        setTasks(prev => prev.filter(t => t.id !== taskId));
        MessagePlugin.info(`任务 "${task.name}" 已取消`);
        break;
    }
  }, [tasks, updateTask, startActualDownload, controllers, removeController, clearResumeData]);

  // 清除已完成的任务
  const clearCompleted = useCallback(() => {
    const completedCount = tasks.filter(t => t.status === 'completed').length;
    const newTasks = tasks.filter(t => t.status !== 'completed');
    setTasks(newTasks);
    // 更新localStorage
    saveTasks(newTasks);
    if (completedCount > 0) {
      NotificationPlugin.success({
        title: '清除完成',
        content: `已清除 ${completedCount} 个已完成的任务`,
        duration: 2000,
      });
    }
  }, [tasks, saveTasks]);

  // 清除失败的任务
  const clearFailed = useCallback(() => {
    const failedCount = tasks.filter(t => t.status === 'failed').length;
    const newTasks = tasks.filter(t => t.status !== 'failed');
    setTasks(newTasks);
    // 更新localStorage
    saveTasks(newTasks);
    if (failedCount > 0) {
      NotificationPlugin.success({
        title: '清除完成',
        content: `已清除 ${failedCount} 个失败的任务`,
        duration: 2000,
      });
    }
  }, [tasks, saveTasks]);

  // 显示/隐藏传输管理器
  const showTransferManager = useCallback(() => {
    setIsVisible(true);
  }, []);

  const hideTransferManager = useCallback(() => {
    setIsVisible(false);
  }, []);

  // 注册上传任务的控制器
  const registerUploadController = useCallback((taskId: string, abortController: AbortController) => {
    addController(taskId, abortController, 'upload');
  }, [addController]);

  // 检查当前并发上传任务数量
  const getRunningUploadCount = useCallback(() => {
    return tasks.filter(task =>
      task.type === 'upload' &&
      (task.status === 'running' || task.status === 'pending')
    ).length;
  }, [tasks]);

  // 检查是否可以创建新的上传任务
  const canCreateNewUploadTask = useCallback(() => {
    const runningCount = getRunningUploadCount();
    return runningCount < MAX_CONCURRENT_UPLOADS;
  }, [getRunningUploadCount]);

  // 上传任务（真实上传，不再模拟）
  const startUploadTask = useCallback((fileName: string, fileSize: number, assetId?: string, totalChunks?: number) => {
    // 检查并发限制
    if (!canCreateNewUploadTask()) {
      const runningCount = getRunningUploadCount();
      MessagePlugin.warning(`当前并发上传任务已达上限（${runningCount}/${MAX_CONCURRENT_UPLOADS}），请等待其他任务完成后再试`);
      throw new Error(`并发上传任务已达上限（${runningCount}/${MAX_CONCURRENT_UPLOADS}）`);
    }

    const taskId = addTask({
      name: fileName,
      type: 'upload',
      status: 'pending',
      progress: 0,
      size: fileSize,
      transferredSize: 0,
      assetId: assetId, // 添加assetId
      totalChunks: totalChunks, // 添加分片总数
    });

    // 返回任务ID，实际上传由外部调用uploadAssetSourceInBackground时传入进度回调
    return taskId;
  }, [addTask, canCreateNewUploadTask, getRunningUploadCount]);

  // 下载任务
  const startDownloadTask = useCallback((fileName: string, fileSize: number, belvethFid?: string, belvethFilename?: string) => {
    const taskId = addTask({
      name: fileName,
      type: 'download',
      status: 'pending',
      progress: 0,
      size: fileSize,
      transferredSize: 0,
      belvethFid, // 添加belvethFid字段
      belvethFilename, // 添加完整文件名字段
    });

    // 如果有belvethFid，启动实际下载；否则使用模拟下载
    if (belvethFid) {
      const task = {
        id: taskId,
        name: fileName,
        type: 'download' as const,
        status: 'pending' as const,
        progress: 0,
        size: fileSize,
        transferredSize: 0,
        belvethFid,
        belvethFilename,
        createdAt: new Date(),
      };
      startActualDownload(task, false); // false表示不是从暂停恢复
      return taskId;
    }

    // 模拟下载进度（用于测试）
    setTimeout(() => {
      updateTask(taskId, { status: 'running' });
      
      const interval = setInterval(() => {
        setTasks(prev => {
          const task = prev.find(t => t.id === taskId);
          if (!task || task.status !== 'running') {
            clearInterval(interval);
            return prev;
          }
          
          const newProgress = Math.min(task.progress + Math.random() * 15, 100);
          const newTransferredSize = Math.floor((newProgress / 100) * task.size);
          const speed = Math.random() * 2 * 1024 * 1024; // 下载通常比上传快
          
          if (newProgress >= 100) {
            clearInterval(interval);
            return prev.map(t => 
              t.id === taskId 
                ? { ...t, progress: 100, transferredSize: t.size, status: 'completed' as const, completedAt: new Date(), speed: undefined }
                : t
            );
          }
          
          return prev.map(t => 
            t.id === taskId 
              ? { ...t, progress: newProgress, transferredSize: newTransferredSize, speed }
              : t
          );
        });
      }, 300);
    }, 500);

    return taskId;
  }, [addTask, updateTask]);

  // 暂停所有正在运行的任务
  const pauseAllTasks = useCallback(() => {
    const runningTasks = tasks.filter(task => task.status === 'running');

    runningTasks.forEach(task => {
      // 调用单个任务的暂停操作
      handleTaskAction(task.id, 'pause');
    });

    if (runningTasks.length > 0) {
      MessagePlugin.info(`已暂停 ${runningTasks.length} 个正在运行的任务`);
    } else {
      MessagePlugin.info('没有正在运行的任务');
    }
  }, [tasks, handleTaskAction]);

  // 恢复所有暂停的任务
  const resumeAllTasks = useCallback(() => {
    const pausedTasks = tasks.filter(task => task.status === 'paused');
    const uploadTasks = pausedTasks.filter(task => task.type === 'upload');
    const downloadTasks = pausedTasks.filter(task => task.type === 'download');

    // 下载任务可以直接恢复
    downloadTasks.forEach(task => {
      handleTaskAction(task.id, 'resume');
    });

    // 上传任务需要用户手动选择文件
    if (uploadTasks.length > 0) {
      MessagePlugin.info(`发现 ${uploadTasks.length} 个暂停的上传任务，请在任务列表中逐个点击恢复按钮重新选择文件`);
    }

    if (downloadTasks.length > 0) {
      MessagePlugin.info(`已恢复 ${downloadTasks.length} 个下载任务`);
    }

    if (pausedTasks.length === 0) {
      MessagePlugin.info('没有暂停的任务');
    }
  }, [tasks, handleTaskAction]);

  // 重试所有失败的任务
  const retryFailedTasks = useCallback(() => {
    const failedTasks = tasks.filter(task => task.status === 'failed');
    const uploadTasks = failedTasks.filter(task => task.type === 'upload');
    const downloadTasks = failedTasks.filter(task => task.type === 'download');

    // 下载任务可以直接重试
    downloadTasks.forEach(task => {
      handleTaskAction(task.id, 'retry');
    });

    // 上传任务需要用户手动重试
    if (uploadTasks.length > 0) {
      MessagePlugin.info(`发现 ${uploadTasks.length} 个失败的上传任务，请在任务列表中逐个点击重试按钮重新选择文件`);
    }

    if (downloadTasks.length > 0) {
      MessagePlugin.info(`已重试 ${downloadTasks.length} 个下载任务`);
    }

    if (failedTasks.length === 0) {
      MessagePlugin.info('没有失败的任务');
    }
  }, [tasks, handleTaskAction]);

  return {
    tasks,
    isVisible,
    addTask,
    updateTask,
    handleTaskAction,
    clearCompleted,
    clearFailed,
    showTransferManager,
    hideTransferManager,
    startUploadTask,
    startDownloadTask,
    pauseAllTasks,
    resumeAllTasks,
    retryFailedTasks,
    registerUploadController,
    getResumeInfo,
    triggerResumeUpload,
    setResumeUploadCallback,
    // 并发控制相关
    getRunningUploadCount,
    canCreateNewUploadTask,
    maxConcurrentUploads: MAX_CONCURRENT_UPLOADS,
  };
};
