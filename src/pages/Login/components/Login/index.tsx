import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button, MessagePlugin } from 'tdesign-react';
import login from '@tencent/tvi-login';
import { useAppDispatch } from 'modules/store';
import { getUserInfo } from 'modules/user';
import { setCurrentAuth } from 'utils/request';

import Style from './index.module.less';

// 使用全局 UMD BvToolkit
declare global {
  interface Window {
    BvToolkit?: any;
  }
}

const TVI_ALLOWED_ORIGIN = /https?:\/\/([\w.-]+\.)?v\.qq\.com$/;

export default function Login() {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [fallbackIframe, setFallbackIframe] = useState(false);
  const toolkitRef = useRef<any>(null);
  // 并发保护：防止重复触发 oauth.check
  const checkingRef = useRef(false);
  const completedRef = useRef(false);

  const handleLoginChecked = useCallback(async () => {
    try {
      // TVI 文档推荐：在 check 成功之后，通过 oauth.auth 回调拿到 Bearer
      let bearer = '';
      try {
        login.oauth.auth((_, value: string) => {
          if (typeof value === 'string') bearer = value;
        });
      } catch (e) {}
      if (bearer) setCurrentAuth(bearer);

      // 再做一次兜底：若 bearer 没有前缀，去掉前缀后写入
      if (!bearer) {
        try {
          let auth = '' as string;
          login.oauth.auth((_, value: string) => {
            auth = (value || '').replace('Bearer ', '');
          });
          if (auth) setCurrentAuth(auth);
        } catch (e) {}
      }

      await dispatch(getUserInfo());
    } catch (e) {}
    MessagePlugin.success('登录成功');
    navigate('/cms/dashboard', { replace: true });
  }, [navigate, dispatch]);

  const onToolkitLogin = useCallback(
    async (res: { code?: string; token?: string } | any) => {
      if (completedRef.current) return; // 已完成无需重复
      if (checkingRef.current) return; // 正在校验中，丢弃重复事件
      checkingRef.current = true;
      try {
        // 优先走推荐流程：check + oauth.auth 回调
        const maxRetry = 2;
        let attempt = 0;
        while (attempt < maxRetry) {
          try {
            const code = (res && (res.code || res.token)) as any; // 兼容部分场景直接返回 token
            await login.oauth.check(code);
            if (!login.oauth.authed) throw new Error('not authed');
            await handleLoginChecked();
            completedRef.current = true;
            return;
          } catch (e: any) {
            attempt += 1;
            const status = e?.response?.status;
            const retryable = status >= 500 || status === 0 || !status; // 网络/5xx 重试
            if (!retryable || attempt >= maxRetry) break;
            await new Promise((r) => setTimeout(r, 300 * attempt)); // 退避
          }
        }

        // 如果 check 持续失败，且 login 事件内含 token/code，则尝试 refresh?token= 的后备路线
        try {
          const tk = res?.token || res?.code;
          if (tk) {
            const url = `https://belveth.v.qq.com/api/oauth/refresh?auth_scene=1&token=${encodeURIComponent(tk)}`;
            const r = await fetch(url, { method: 'GET', credentials: 'include' });
            let j: any = null;
            try { j = await r.clone().json(); } catch {}
            if (j?.auth && typeof j.auth === 'string') {
              setCurrentAuth(j.auth);
              await handleLoginChecked();
              completedRef.current = true;
              return;
            }
          }
        } catch {}

        MessagePlugin.error('登录异常');
      } finally {
        checkingRef.current = false;
      }
    },
    [handleLoginChecked],
  );

  useEffect(() => {
    if ((login as any)?.oauth?.authed) {
      navigate('/cms/dashboard', { replace: true });
      return;
    }

    const checkBvToolkit = async () => {
      if (!window.BvToolkit) {
        const timer = setInterval(() => {
          if (window.BvToolkit) {
            clearInterval(timer);
            initLogin();
          }
        }, 500);
      } else {
        initLogin();
      }
    };

    const initLogin = () => {
      try {
        const loginToolkit = new window.BvToolkit.Login({
          id: 'login-toolkit',
          // 增加 IOA 登录入口，并将 IOA 排在首位
          scenes: ['ioa', 'weixin', 'sms'] as any[],
        });
        toolkitRef.current = loginToolkit;
        loginToolkit.start();

        const dispatcher = loginToolkit.getEventDispatcher();
        dispatcher.on('login', onToolkitLogin);

        return () => {
          dispatcher.off('login', onToolkitLogin as any);
          loginToolkit.destroy();
        };
      } catch (e: any) {
        console.error('BvToolkit 初始化失败:', e);
        console.warn('BVToolkit init failed, fallback to iframe:', e?.message || e);
        setFallbackIframe(true);
      }
    };

    checkBvToolkit();
  }, [navigate, onToolkitLogin]);

  useEffect(() => {
    if (!fallbackIframe) return;
    const onMessage = async (event: MessageEvent) => {
      if (!TVI_ALLOWED_ORIGIN.test(event.origin)) return;
      if (completedRef.current || checkingRef.current) return;
      const data: any = event.data || {};
      try {
        const codeOrToken = data?.token || data?.code || (data?.type === 'tvi-login' ? (data.code || data.token) : undefined);
        if (!codeOrToken) return;
        checkingRef.current = true;
        let attempt = 0;
        const maxRetry = 2;
        while (attempt < maxRetry) {
          try {
            await login.oauth.check(codeOrToken);
            if (!login.oauth.authed) throw new Error('not authed');
            await handleLoginChecked();
            completedRef.current = true;
            return;
          } catch (e: any) {
            attempt += 1;
            const status = e?.response?.status;
            const retryable = status >= 500 || status === 0 || !status;
            if (!retryable || attempt >= maxRetry) throw e;
            await new Promise((r) => setTimeout(r, 300 * attempt));
          }
        }
      } catch (err) {
        console.error('TVI iframe login failed:', err);
        MessagePlugin.error('登录异常');
      } finally {
        checkingRef.current = false;
      }
    };
    window.addEventListener('message', onMessage);
    return () => window.removeEventListener('message', onMessage);
  }, [fallbackIframe, handleLoginChecked]);

  return (
    <div className={fallbackIframe ? Style.iframeContainer : Style.itemContainer}>
      {!fallbackIframe && <div id="login-toolkit" className={Style.loginContainer} />}
      {fallbackIframe && (
        <div>
          <div style={{ marginBottom: 12 }}>
            <Button
              theme="primary"
              onClick={() => {
                window.open('https://tvi.v.qq.com/', '_blank', 'noopener,noreferrer');
              }}
            >在新窗口打开登录（推荐）</Button>
          </div>
          <iframe
            title="tvi-login"
            src="https://tvi.v.qq.com/"
            style={{ width: '100%', height: 800, border: 'none' }}
            allow="publickey-credentials-get *; payment *; clipboard-write *; fullscreen *; autoplay *; camera *; microphone *"
          />
        </div>
      )}
    </div>
  );
}