import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Space,

  message,
  Dialog,
  Form,
  Select,
  Textarea,
  Pagination,
} from 'tdesign-react';
import { SearchIcon, EditIcon, BrowseIcon } from 'tdesign-icons-react';
import ThumbnailUpload from '../../../components/ThumbnailUpload';
import FidImage from '../../../components/FidImage';
import { FilmSessionService, FilmSession, FilmSessionUpdateData } from '../../../services/filmSessions';
import { SceneService, Scene } from '../../../services/scenes';
import { ProjectService, Project } from '../../../services/projects';
import { FileUploadService } from '../../../services/fileUpload';
import { instance } from '../../../utils/request';
import { useAppSelector, useAppDispatch } from '../../../modules/store';
import { getUserInfo } from '../../../modules/user';
import { RenderOptimizer } from '../../../utils/performanceOptimizer';

const { FormItem } = Form;
const { Option } = Select;

const FilmSessionsPage: React.FC = () => {
  // 获取用户信息
  const dispatch = useAppDispatch();
  const userState = useAppSelector((state) => state.user);

  // 确保用户信息已加载
  useEffect(() => {
    if (!userState.userInfo || Object.keys(userState.userInfo).length === 0) {
      dispatch(getUserInfo());
    }
  }, [dispatch, userState.userInfo]);

  // 基础状态
  const [loading, setLoading] = useState(false);
  const [filmSessions, setFilmSessions] = useState<FilmSession[]>([]);
  const [scenes, setScenes] = useState<Scene[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 搜索和筛选状态
  const [searchValue, setSearchValue] = useState('');
  const [sceneFilter, setSceneFilter] = useState<string>('');
  const [projectFilter, setProjectFilter] = useState<string>('');

  // 根据选择的项目过滤场景列表
  const filteredScenes = projectFilter
    ? scenes.filter(scene => scene.project_id === projectFilter)
    : scenes;

  // 编辑弹窗状态
  const [modalVisible, setModalVisible] = useState(false);
  const [editingSession, setEditingSession] = useState<FilmSession | null>(null);
  const [form] = Form.useForm();
  const thumbnailFileRef = useRef<File | null>(null);

  // 弹窗中的级联选择状态
  const [modalProjectId, setModalProjectId] = useState<string>('');

  // 弹窗中根据选择的项目过滤场景列表
  const modalFilteredScenes = modalProjectId
    ? scenes.filter(scene => scene.project_id === modalProjectId)
    : scenes;

  // 查看弹窗状态
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [viewingSession, setViewingSession] = useState<FilmSession | null>(null);

  // 获取项目列表
  const fetchProjects = async () => {
    try {
      const response = await ProjectService.getList({ limit: 100 });
      setProjects(response.list || []);
    } catch (error) {
      console.error('获取项目列表失败:', error);
    }
  };

  // 获取场景列表
  const fetchScenes = async () => {
    try {
      const response = await SceneService.getList({ page_size: 100 });
      setScenes(response.scene || []);
    } catch (error) {
      console.error('获取场景列表失败:', error);
    }
  };

  // 获取场次列表
  const fetchFilmSessions = async () => {
    try {
      setLoading(true);
      const params: any = {
        page: currentPage,
        page_size: pageSize,
      };

      if (searchValue) {
        params.search = searchValue;
      }
      if (sceneFilter) {
        params.scene_id = sceneFilter;
      }
      if (projectFilter) {
        params.project_id = projectFilter;
      }

      const response = await FilmSessionService.searchList(params);
      setFilmSessions(response.data || []);
      setTotal(response.total || 0);
    } catch (error) {
      message.error('获取场次列表失败');
      console.error('获取场次列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProjects();
    fetchScenes();
  }, []);

  useEffect(() => {
    fetchFilmSessions();
  }, [currentPage, pageSize, searchValue, sceneFilter, projectFilter]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      // 防抖函数会自动清理
    };
  }, []);

  // 搜索 - 使用防抖优化
  const handleSearch = RenderOptimizer.debounce((value: string) => {
    setSearchValue(value);
    setCurrentPage(1);
  }, 300);

  // 项目筛选变化处理 - 级联清空场景筛选
  const handleProjectFilterChange = (value: string) => {
    console.log('项目筛选变化:', value);
    console.log('当前项目下的场景数量:', scenes.filter(scene => scene.project_id === value).length);
    setProjectFilter(value);
    setSceneFilter(''); // 清空场景筛选，因为场景列表已经变化
    setCurrentPage(1);
  };

  // 重置搜索
  const handleReset = () => {
    setSearchValue('');
    setSceneFilter('');
    setProjectFilter('');
    setCurrentPage(1);
  };

  // 弹窗中项目选择变化处理
  const handleModalProjectChange = (value: string) => {
    console.log('弹窗项目选择变化:', value);
    const currentProjectId = form.getFieldValue('project_id');

    setModalProjectId(value);

    // 如果项目发生变化，清空场景选择并提示用户
    if (currentProjectId && currentProjectId !== value) {
      form.setFieldsValue({ scene_id: undefined });
      message.info('项目已更改，请重新选择场景');
    }
  };



  // 打开查看弹窗
  const handleView = (session: FilmSession) => {
    setViewingSession(session);
    setViewModalVisible(true);
  };

  // 打开编辑弹窗
  const handleEdit = async (session: FilmSession) => {
    setEditingSession(session);
    thumbnailFileRef.current = null;
    form.reset();

    // 获取场景对应的项目ID
    const scene = scenes.find(s => s.id === session.scene_id);
    const projectId = session.project_id || scene?.project_id || '';

    // 设置弹窗的项目选择状态
    setModalProjectId(projectId);

    setTimeout(() => {
      form.setFieldsValue({
        name: session.name,
        project_id: projectId,
        scene_id: session.scene_id,
        data: session.data,
        description: session.description,
        thumbnail_fid: session.thumbnail_fid,
      });
    }, 0);
    setModalVisible(true);
  };

  // 获取场景名称
  const getSceneName = (sceneId: string) => {
    const scene = scenes.find(s => s.id === sceneId);
    return scene?.name || sceneId;
  };

  // 获取项目名称
  const getProjectName = (projectId: string) => {
    const project = projects.find(p => p.id === projectId);
    return project?.name || projectId;
  };





  // 提交表单
  const handleSubmit = async () => {
    try {
      const validateResult = await form.validate();
      if (validateResult === true) {
        const values = form.getFieldsValue(true);

        // 处理缩略图上传
        let finalThumbnailFid = values.thumbnail_fid || '';
        const localThumbFile: File | undefined = thumbnailFileRef.current || undefined;
        if (localThumbFile) {
          try {
            // 计算 MD5 与大小
            const md5 = await FileUploadService.calculateFileMD5(localThumbFile);
            const size = localThumbFile.size;
            // 依据 fid 规范拼接 name 参数
            const base = ((instance.defaults as any)?.baseURL || '').replace(/\/$/, '');
            const url = new URL(base + '/single_file_upload');
            url.searchParams.set('name', 'test/{fid}.jpg');
            url.searchParams.set('size', String(size));
            url.searchParams.set('h', md5);
            const res: any = await instance.request({
              url: url.toString(),
              method: 'PUT',
              data: localThumbFile,
              headers: { 'Content-Type': localThumbFile.type || 'application/octet-stream' },
            });
            const fid = res?.fid || res?.data?.fid || res?.data?.data?.fid || '';
            if (!fid) throw new Error('single_file_upload 未返回 fid');
            finalThumbnailFid = fid;
          } catch (e) {
            message.error((e as any)?.message || '缩略图上传失败');
            return;
          }
        }

        if (editingSession) {
          // 更新场次
          const projectId = values.project_id;

          if (!projectId) {
            message.error('请选择所属项目');
            return;
          }

          if (!values.scene_id) {
            message.error('请选择所属场景');
            return;
          }

          const updateData: FilmSessionUpdateData = {
            data: values.data,
            project_id: projectId,
            session_id: editingSession.id,
            version: editingSession.version + 1, // 版本号递增
            thumbnail_fid: finalThumbnailFid,
            desc: values.description || '',
          };
          await FilmSessionService.update(updateData);
          message.success('更新场次成功');
        }

        setModalVisible(false);
        setModalProjectId(''); // 清理弹窗状态
        fetchFilmSessions();
      }
    } catch (error) {
      message.error('更新场次失败');
      console.error('提交表单失败:', error);
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card title="场次管理" bordered={false}>
        {/* 搜索区域 */}
        <div style={{ marginBottom: '16px' }}>
          <Space>
            <Input
              placeholder="搜索场次名称"
              prefixIcon={<SearchIcon />}
              style={{ width: '200px' }}
              onChange={(value) => handleSearch(value)}
              clearable
            />
            <Select
              placeholder="选择项目"
              style={{ width: '150px' }}
              value={projectFilter}
              onChange={(value) => handleProjectFilterChange(value as string)}
              clearable
            >
              {projects.map(project => (
                <Option key={project.id} value={project.id}>
                  {project.name}
                </Option>
              ))}
            </Select>
            <Select
              placeholder={projectFilter ? "选择场景" : "请先选择项目"}
              style={{ width: '150px' }}
              value={sceneFilter}
              onChange={(value) => setSceneFilter(value as string)}
              clearable
              disabled={!projectFilter}
            >
              {filteredScenes.map((scene: Scene) => (
                <Option key={scene.id} value={scene.id}>
                  {scene.name}
                </Option>
              ))}
            </Select>
            <Button onClick={handleReset}>重置</Button>
          </Space>
        </div>

        {/* 表格区域 */}
        <Table
          data={filmSessions}
          columns={[
            {
              title: '缩略图',
              colKey: 'thumbnail_fid',
              width: 80,
              cell: ({ row }: { row: FilmSession }) => (
                <FidImage
                  fidOrUrl={row.thumbnail_fid || ''}
                  width={60}
                  height={45}
                  style={{ borderRadius: '4px' }}
                />
              ),
            },
            {
              title: '场次名称',
              colKey: 'name',
              width: 200,
            },
            {
              title: '所属场景',
              colKey: 'scene_id',
              width: 150,
              cell: ({ row }: { row: FilmSession }) => (
                <span>{getSceneName(row.scene_id)}</span>
              ),
            },
            {
              title: '所属项目',
              colKey: 'project_id',
              width: 150,
              cell: ({ row }: { row: FilmSession }) => {
                // 优先使用后端返回的project_id，如果没有则通过scene查找
                const projectId = row.project_id || scenes.find(s => s.id === row.scene_id)?.project_id || '';
                return <span>{projectId ? getProjectName(projectId) : '未知项目'}</span>;
              },
            },
            {
              title: '版本',
              colKey: 'version',
              width: 80,
            },
            {
              title: '描述',
              colKey: 'description',
              width: 200,
              ellipsis: true,
            },
            {
              title: '操作',
              colKey: 'action',
              width: 150,
              cell: ({ row }: { row: FilmSession }) => (
                <Space>
                  <Button
                    size="small"
                    variant="text"
                    icon={<BrowseIcon />}
                    onClick={() => handleView(row)}
                  >
                    查看
                  </Button>
                  <Button
                    size="small"
                    variant="text"
                    icon={<EditIcon />}
                    onClick={() => handleEdit(row)}
                  >
                    编辑
                  </Button>
                </Space>
              ),
            },
          ]}
          loading={loading}
          rowKey="id"
        />

        {/* 分页区域 - 将在下一步实现 */}
        <div style={{ marginTop: '16px', textAlign: 'right' }}>
          <Pagination
            current={currentPage}
            total={total}
            pageSize={pageSize}
            showJumper
            showPageSize
            onChange={(pageInfo) => {
              setCurrentPage(pageInfo.current);
              setPageSize(pageInfo.pageSize);
            }}
          />
        </div>
      </Card>

      {/* 编辑弹窗 */}
      <Dialog
        header="编辑场次"
        visible={modalVisible}
        onClose={() => {
          setModalVisible(false);
          setModalProjectId(''); // 清理弹窗状态
        }}
        onConfirm={handleSubmit}
        width={600}
        confirmBtn="保存"
        cancelBtn="取消"
      >
        <Form form={form} layout="vertical">
          <FormItem
            label="场次名称"
            name="name"
            rules={[{ required: true, message: '请输入场次名称' }]}
          >
            <Input placeholder="请输入场次名称" />
          </FormItem>

          <FormItem
            label="所属项目"
            name="project_id"
            rules={[{ required: true, message: '请选择所属项目' }]}
          >
            <Select
              placeholder="请选择所属项目"
              onChange={(value) => handleModalProjectChange(value as string)}
            >
              {projects.map(project => (
                <Option key={project.id} value={project.id}>
                  {project.name}
                </Option>
              ))}
            </Select>
          </FormItem>

          <FormItem
            label="所属场景"
            name="scene_id"
            rules={[
              { required: true, message: '请选择所属场景' }
            ]}
          >
            <Select
              placeholder={modalProjectId ? "请选择所属场景" : "请先选择项目"}
              disabled={!modalProjectId}
            >
              {modalFilteredScenes.map((scene: Scene) => (
                <Option key={scene.id} value={scene.id}>
                  {scene.name}
                </Option>
              ))}
            </Select>
          </FormItem>

          <FormItem
            label="数据"
            name="data"
            rules={[{ required: true, message: '请输入数据' }]}
          >
            <Input placeholder="请输入数据" />
          </FormItem>

          <FormItem label="描述" name="description">
            <Textarea placeholder="请输入描述" rows={3} />
          </FormItem>

          <FormItem label="缩略图" name="thumbnail_fid">
            <ThumbnailUpload
              value={Form.useWatch('thumbnail_fid', form)}
              onChange={(value, file) => {
                // 只用于展示路径（fid 或 URL），以及缓存本地文件以供提交阶段上传
                form.setFieldsValue({ thumbnail_fid: value });
                thumbnailFileRef.current = file || null;
              }}
              previewOnly
            />
          </FormItem>
        </Form>
      </Dialog>

      {/* 查看弹窗 */}
      <Dialog
        header="查看场次详情"
        visible={viewModalVisible}
        onClose={() => setViewModalVisible(false)}
        width={600}
        footer={
          <Button onClick={() => setViewModalVisible(false)}>
            关闭
          </Button>
        }
      >
        {viewingSession && (
          <div style={{ padding: '16px 0' }}>
            <div style={{ marginBottom: '16px' }}>
              <strong>缩略图：</strong>
              <div style={{ marginTop: '8px' }}>
                <FidImage
                  fidOrUrl={viewingSession.thumbnail_fid || ''}
                  width={120}
                  height={90}
                  style={{ borderRadius: '4px' }}
                />
              </div>
            </div>

            <div style={{ marginBottom: '16px' }}>
              <strong>场次名称：</strong>
              <span style={{ marginLeft: '8px' }}>{viewingSession.name}</span>
            </div>

            <div style={{ marginBottom: '16px' }}>
              <strong>所属场景：</strong>
              <span style={{ marginLeft: '8px' }}>{getSceneName(viewingSession.scene_id)}</span>
            </div>

            <div style={{ marginBottom: '16px' }}>
              <strong>所属项目：</strong>
              <span style={{ marginLeft: '8px' }}>
                {(() => {
                  const projectId = viewingSession.project_id || scenes.find(s => s.id === viewingSession.scene_id)?.project_id || '';
                  return projectId ? getProjectName(projectId) : '未知项目';
                })()}
              </span>
            </div>

            <div style={{ marginBottom: '16px' }}>
              <strong>数据：</strong>
              <span style={{ marginLeft: '8px' }}>{viewingSession.data}</span>
            </div>

            <div style={{ marginBottom: '16px' }}>
              <strong>版本：</strong>
              <span style={{ marginLeft: '8px' }}>{viewingSession.version}</span>
            </div>

            <div style={{ marginBottom: '16px' }}>
              <strong>描述：</strong>
              <div style={{ marginTop: '8px', padding: '8px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
                {viewingSession.description || '暂无描述'}
              </div>
            </div>
          </div>
        )}
      </Dialog>
    </div>
  );
};

export default FilmSessionsPage;
