import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Space,
  Tag,
  Popconfirm,
  message,
  Dialog,
  Form,
  Select,
  Textarea,

  Pagination,
  MessagePlugin,
} from 'tdesign-react';
import { SearchIcon, AddIcon, EditIcon, DeleteIcon, BrowseIcon } from 'tdesign-icons-react';
import ThumbnailUpload from '../../../components/ThumbnailUpload';
import { FileUploadService } from '../../../services/fileUpload';
import { instance } from '../../../utils/request';
import MultiAssetSelector from '../../../components/MultiAssetSelector';
import SourceFileManager from '../../../components/SourceFileManager';
import { ProjectService, Project } from '../../../services/projects';
import { Asset } from '../../../services/assets';
import { RenderOptimizer } from '../../../utils/performanceOptimizer';

import dayjs from 'dayjs';
import FidImage from '../../../components/FidImage';

const { FormItem } = Form;
const { Option } = Select;

const ProjectsPage: React.FC = () => {


  const [loading, setLoading] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchValue, setSearchValue] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  // 弹窗状态
  const [modalVisible, setModalVisible] = useState(false);
  const [modalType, setModalType] = useState<'create' | 'edit'>('create');
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [form] = Form.useForm();
  // 监听缩略图字段值变化
  const thumbnailPath = Form.useWatch('thumbnail_path', form);
  // 本地选择的缩略图文件（避免 Form 未注册字段导致取不到）
  const thumbnailFileRef = React.useRef<File | null>(null);

  // 角色资产选择相关状态
  const [assetSelectorVisible, setAssetSelectorVisible] = useState(false);
  const [selectedAssets, setSelectedAssets] = useState<Asset[]>([]);

  // 查看项目相关状态
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [viewingProject, setViewingProject] = useState<Project | null>(null);

  // 状态选项
  const statusOptions = [
    { label: '禁用', value: 0 },
    { label: '启用', value: 1 },
    { label: '草稿', value: 2 },
  ];

  // 获取项目列表
  const fetchProjects = async () => {
    setLoading(true);
    try {
      const response = await ProjectService.getList({
        page: currentPage,
        limit: pageSize,
        search: searchValue,
      });
      setProjects(response.list);
      setTotal(response.pagination.total);
    } catch (error) {
      message.error('获取项目列表失败');
      console.error('获取项目列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProjects();
  }, [currentPage, pageSize, searchValue]);

  // 当编辑项目改变时，更新表单值
  useEffect(() => {
    if (editingProject && modalVisible && modalType === 'edit') {
      form.setFieldsValue({
        name: editingProject.name,
        description: editingProject.description,
        status: editingProject.status,
        thumbnail_path: editingProject.thumbnail_path,
      });
    }
  }, [editingProject, modalVisible, modalType, form]);

  // 搜索 - 使用防抖优化
  const handleSearch = RenderOptimizer.debounce((value: string) => {
    setSearchValue(value);
    setCurrentPage(1);
  }, 300);

  // 重置搜索
  const handleReset = () => {
    setSearchValue('');
    setCurrentPage(1);
  };

  // 打开创建弹窗
  const handleCreate = () => {
    setModalType('create');
    setEditingProject(null);
    setSelectedAssets([]); // 清空选择的资产
    form.reset();
    // 使用 setTimeout 确保表单重置后再设置默认值
    setTimeout(() => {
      form.setFieldsValue({
        status: 1,
      });
    }, 0);
    setModalVisible(true);
  };

  // 打开编辑弹窗
  const handleEdit = async (project: Project) => {
    setModalType('edit');
    setEditingProject(project);

    // 获取项目详情（包含关联的资产）
    try {
      const projectDetail = await ProjectService.getById(project.id);
      // 过滤掉空字符串的资产ID
      const validAssets = (projectDetail.assets || []).filter((asset: Asset) => asset.id && asset.id.trim() !== '');
      setSelectedAssets(validAssets);
    } catch (error) {
      console.error('获取项目资产失败:', error);
      setSelectedAssets([]);
    }

    // 先重置表单，然后设置值
    form.reset();
    setTimeout(() => {
      form.setFieldsValue({
        name: project.name,
        description: project.description,
        status: project.status,
        thumbnail_path: project.thumbnail_path,
      });
    }, 0);
    setModalVisible(true);
  };

  // 打开角色资产选择器
  const handleOpenAssetSelector = () => {
    setAssetSelectorVisible(true);
  };

  // 确认选择角色资产
  const handleConfirmAssets = (assets: Asset[]) => {
    setSelectedAssets(assets);
  };

  // 移除角色资产
  const handleRemoveAsset = (assetId: string) => {
    setSelectedAssets(prev => prev.filter(asset => asset.id !== assetId));
  };

  // 打开查看弹窗
  const handleView = async (project: Project) => {
    try {
      // 获取项目详情（包含关联的资产）
      const projectDetail = await ProjectService.getById(project.id);
      setViewingProject(projectDetail);
      setViewModalVisible(true);
    } catch (error) {
      console.error('获取项目详情失败:', error);
      message.error('获取项目详情失败');
    }
  };

  // 移除了简单的下载函数，使用SourceFileManager组件替代

  // 提交表单
  const handleSubmit = async () => {
    try {
      const validateResult = await form.validate();
      if (validateResult === true) {
        const values = form.getFieldsValue(true);

        // 1) 若选择了本地缩略图文件，先走 single_file_upload 获取 fid
        let thumbnailFid: string | undefined;
        const localThumbFile: File | undefined = thumbnailFileRef.current || undefined;
        if (localThumbFile) {
          try {
            // 计算 MD5 与大小
            const md5 = await FileUploadService.calculateFileMD5(localThumbFile);
            const size = localThumbFile.size;
            // 依据 fid 规范拼接 name 参数；若没有显式 fid，这里使用 md5 作为文件名以避免重复
            const base = ((instance.defaults as any)?.baseURL || '').replace(/\/$/, '');
            const url = new URL(base + '/single_file_upload');
            // 按要求硬编码 name=test/{fid}.jpg（注意：此处 {fid} 为字面量，不替换）
            url.searchParams.set('name', 'test/{fid}.jpg');
            url.searchParams.set('size', String(size));
            url.searchParams.set('h', md5);
            const res: any = await instance.request({
              url: url.toString(),
              method: 'PUT',
              data: localThumbFile,
              headers: { 'Content-Type': localThumbFile.type || 'application/octet-stream' },
            });
            const fid = res?.fid || res?.data?.fid || res?.data?.data?.fid || '';
            if (!fid) throw new Error('single_file_upload 未返回 fid');
            thumbnailFid = fid;
          } catch (e) {
            MessagePlugin.error((e as any)?.message || '缩略图上传失败');
            return;
          }
        }

        if (modalType === 'create') {
          const createData: any = {
            name: values.name,
            desc: values.description,
            status: values.status,
            thumbnail_fid: thumbnailFid || values.thumbnail_path || '',
            asset_list: selectedAssets.filter(asset => asset.id && asset.id.trim() !== '').map(asset => asset.id),
          };
          await instance.post('/core/create_project', createData);
          message.success('创建项目成功');
        } else {
          const updateData: any = {
            name: values.name,
            desc: values.description,
            status: values.status,
            thumbnail_fid: thumbnailFid || values.thumbnail_path || '',
            asset_list: selectedAssets.filter(asset => asset.id && asset.id.trim() !== '').map(asset => asset.id),
          };
          await instance.post('/core/update_project', { project_id: editingProject!.id, ...updateData });
          message.success('更新项目成功');
        }

        setModalVisible(false);
        fetchProjects();
      }
    } catch (error) {
      message.error(modalType === 'create' ? '创建项目失败' : '更新项目失败');
      console.error('提交表单失败:', error);
    }
  };

  // 删除项目
  const handleDelete = async (id: string) => {
    try {
      await ProjectService.delete(id);
      message.success('删除项目成功');
      fetchProjects();
    } catch (error) {
      message.error('删除项目失败');
      console.error('删除项目失败:', error);
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的项目');
      return;
    }

    try {
      // 逐个删除，失败不中断，统计成功数量
      let success = 0;
      for (const id of selectedRowKeys) {
        try {
          // eslint-disable-next-line no-await-in-loop
          await ProjectService.delete(id as string);
          success += 1;
        } catch (e) {
          // 单个失败仅记录，继续
          console.error('批量删除单项失败:', id, e);
        }
      }
      if (success > 0) {
        message.success(`批量删除完成，成功 ${success}/${selectedRowKeys.length}`);
        setSelectedRowKeys([]);
        fetchProjects();
      } else {
        message.error('批量删除失败');
      }
    } catch (error) {
      message.error('批量删除失败');
      console.error('批量删除失败:', error);
    }
  };

  // 表格列配置
  const columns = [
    {
      colKey: 'row-select',
      type: 'multiple' as const,
      width: 50,
    },
    {
      title: '缩略图',
      colKey: 'thumbnail_path',
      width: 80,
      cell: ({ row }: { row: Project }) => (
        <FidImage fidOrUrl={row.thumbnail_path || ''} width={60} height={45} />
      ),
    },
    { title: '项目名称', colKey: 'name', width: 240 },
    { title: '创建人', colKey: 'created_by', width: 160 },
    { title: '更新人', colKey: 'updated_by', width: 160 },
    {
      title: '状态', colKey: 'status', width: 100,
      cell: ({ row }: { row: Project & any }) => {
        const status = Number(row.status);
        const info = status === 1
          ? { label: '启用', theme: 'success' as const }
          : status === 2
          ? { label: '禁用', theme: 'danger' as const }
          : { label: '未知', theme: 'default' as const };
        return (
          <Button size="small" variant="base" theme={info.theme} style={{ padding: '0 10px', height: 24, lineHeight: '22px', borderRadius: 12, fontWeight: 600 }}>
            {info.label}
          </Button>
        );
      },
    },
    {
      title: '创建时间', colKey: 'created_at', width: 180,
      cell: ({ row }: { row: Project & any }) => dayjs(Number(row.created_at) * 1000).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '更新时间', colKey: 'updated_at', width: 180,
      cell: ({ row }: { row: Project & any }) => dayjs(Number(row.updated_at) * 1000).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      colKey: 'action',
      width: 200,
      cell: ({ row }: { row: Project }) => (
        <Space>
          <Button
            size="small"
            variant="text"
            icon={<BrowseIcon />}
            onClick={() => handleView(row)}
          >
            查看
          </Button>
          <Button
            size="small"
            variant="text"
            icon={<EditIcon />}
            onClick={() => handleEdit(row)}
          >
            编辑
          </Button>
          <Popconfirm
            content="确定要删除这个项目吗？"
            onConfirm={() => handleDelete(row.id)}
          >
            <Button
              size="small"
              variant="text"
              theme="danger"
              icon={<DeleteIcon />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card title="项目管理" bordered={false}>
        {/* 搜索栏 */}
        <div style={{ marginBottom: '16px' }}>
          <Space>
            <Input
              placeholder="搜索项目名称或描述"
              value={searchValue}
              onChange={(value) => handleSearch(value)}
              style={{ width: '300px' }}
              suffixIcon={<SearchIcon />}
            />

            <Button variant="outline" onClick={handleReset}>重置</Button>
          </Space>
        </div>

        {/* 操作栏 */}
        <div style={{ marginBottom: '16px' }}>
          <Space>
            <Button theme="primary" icon={<AddIcon />} onClick={handleCreate}>
              新建项目
            </Button>
            <Popconfirm
              content="确定要批量删除选中的项目吗？"
              onConfirm={handleBatchDelete}
            >
              <Button
                theme="danger"
                variant="outline"
                disabled={selectedRowKeys.length === 0}
              >
                批量删除 ({selectedRowKeys.length})
              </Button>
            </Popconfirm>
          </Space>
        </div>

        {/* 表格 */}
        <Table
          data={projects}
          columns={columns}
          loading={loading}
          rowKey="id"
          selectedRowKeys={selectedRowKeys}
          onSelectChange={(selectedRowKeys: (string | number)[]) => {
            setSelectedRowKeys(selectedRowKeys as string[]);
          }}
        />

        {/* 分页 */}
        <div style={{ marginTop: '16px', textAlign: 'right' }}>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={total}
            onChange={(pageInfo: { current: number; pageSize: number }) => {
              setCurrentPage(pageInfo.current);
              setPageSize(pageInfo.pageSize);
            }}
          />
          <div style={{ marginTop: '8px', fontSize: '14px', color: '#666' }}>
            共 {total} 条记录
          </div>
        </div>
      </Card>

      {/* 创建/编辑弹窗 */}
      <Dialog
        header={modalType === 'create' ? '新建项目' : '编辑项目'}
        visible={modalVisible}
        onCancel={() => {
          console.log('Dialog onCancel triggered');
          setModalVisible(false);
        }}
        onClose={() => {
          console.log('Dialog onClose triggered');
          setModalVisible(false);
        }}
        width={600}
        closeBtn={true}
        closeOnEscKeydown={true}
        closeOnOverlayClick={true}
        footer={
          <Space>
            <Button onClick={() => setModalVisible(false)}>取消</Button>
            <Button theme="primary" onClick={handleSubmit}>
              {modalType === 'create' ? '创建' : '保存'}
            </Button>
          </Space>
        }
      >
        <Form form={form} layout="vertical">
          <FormItem
            label="项目名称"
            name="name"
            rules={[{ required: true, message: '请输入项目名称' }]}
          >
            <Input placeholder="请输入项目名称" />
          </FormItem>

          <FormItem label="项目描述" name="description" rules={[{ required: true, message: '请输入项目描述' }] }>
            <Textarea placeholder="请输入项目描述" rows={3} />
          </FormItem>

          <FormItem label="状态" name="status">
            <Select placeholder="请选择状态">
              <Option key={1} value={1}>启用</Option>
              <Option key={2} value={2}>禁用</Option>
            </Select>
          </FormItem>

          <FormItem label="缩略图" name="thumbnail_path">
            <ThumbnailUpload
              value={thumbnailPath as string}
              onChange={(value, file) => {
                // 只用于展示路径（fid 或 URL），以及缓存本地文件以供提交阶段上传
                form.setFieldsValue({ thumbnail_path: value });
                thumbnailFileRef.current = file || null;
              }}
              previewOnly
            />
          </FormItem>

          <FormItem label="角色资产">
            <div style={{
              border: '1px solid #e7e7e7',
              borderRadius: '6px',
              padding: '16px',
              backgroundColor: '#fff'
            }}>
              {selectedAssets.filter(asset => asset.id && asset.id.trim() !== '').length > 0 ? (
                <div>
                  <div style={{
                    marginBottom: '12px',
                    fontSize: '14px',
                    fontWeight: 500
                  }}>
                    已选择 {selectedAssets.filter(asset => asset.id && asset.id.trim() !== '').length} 个角色资产：
                  </div>

                  {/* 显示角色资产缩略图，最多3个 */}
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fill, minmax(120px, 1fr))',
                    gap: '12px',
                    marginBottom: '16px',
                    maxWidth: '380px'
                  }}>
                    {selectedAssets.filter(asset => asset.id && asset.id.trim() !== '').slice(0, 3).map(asset => (
                      <div
                        key={asset.id}
                        style={{
                          border: '1px solid #e7e7e7',
                          borderRadius: '6px',
                          padding: '8px',
                          backgroundColor: '#fff',
                          position: 'relative'
                        }}
                      >
                        {/* 缩略图 */}
                        <div style={{
                          textAlign: 'center',
                          marginBottom: '8px'
                        }}>
                          {asset.thumbnail_path ? (
                            <FidImage fidOrUrl={asset.thumbnail_path} width={'100%'} height={80} style={{ objectFit: 'cover', borderRadius: 4 }} />
                          ) : (
                            <div style={{
                              width: '100%',
                              height: '80px',
                              backgroundColor: '#f5f5f5',
                              borderRadius: '4px',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              color: '#999',
                              fontSize: '12px'
                            }}>
                              暂无预览图
                            </div>
                          )}
                        </div>

                        {/* 资产名称 */}
                        <div style={{
                          fontSize: '12px',
                          textAlign: 'center',
                          marginBottom: '8px',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}>
                          {asset.name}
                        </div>

                        {/* 删除按钮 */}
                        <div style={{
                          textAlign: 'center'
                        }}>
                          <Button
                            size="small"
                            variant="text"
                            theme="danger"
                            onClick={() => handleRemoveAsset(asset.id)}
                            style={{ fontSize: '12px', padding: '2px 8px' }}
                          >
                            移除
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* 如果有超过3个资产，显示省略提示 */}
                  {selectedAssets.filter(asset => asset.id && asset.id.trim() !== '').length > 3 && (
                    <div style={{
                      fontSize: '12px',
                      color: '#666',
                      marginBottom: '12px'
                    }}>
                      还有 {selectedAssets.filter(asset => asset.id && asset.id.trim() !== '').length - 3} 个资产未显示...
                    </div>
                  )}

                  <div style={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: '8px',
                    marginBottom: '12px'
                  }}>
                    {selectedAssets.filter(asset => asset.id && asset.id.trim() !== '').map(asset => (
                      <Tag
                        key={asset.id}
                        closable
                        onClose={() => handleRemoveAsset(asset.id)}
                      >
                        {asset.name}
                      </Tag>
                    ))}
                  </div>
                  <Button size="small" onClick={handleOpenAssetSelector}>
                    添加更多资产
                  </Button>
                </div>
              ) : (
                <div style={{ textAlign: 'center', padding: '20px 0' }}>
                  <div style={{
                    color: '#999',
                    marginBottom: '12px',
                    fontSize: '14px'
                  }}>
                    请选择角色资产
                  </div>
                  <Button onClick={handleOpenAssetSelector}>
                    选择角色资产
                  </Button>
                </div>
              )}
            </div>
          </FormItem>


        </Form>
      </Dialog>

      {/* 角色资产选择弹窗 */}
      <MultiAssetSelector
        visible={assetSelectorVisible}
        onClose={() => setAssetSelectorVisible(false)}
        onConfirm={handleConfirmAssets}
        selectedAssets={selectedAssets}
        assetTypeId={1} // 角色模型类型
      />

      {/* 查看项目详情弹窗 */}
      <Dialog
        header="项目详情"
        visible={viewModalVisible}
        onClose={() => setViewModalVisible(false)}
        width={800}
        closeBtn={true}
        closeOnEscKeydown={true}
        closeOnOverlayClick={true}
        footer={
          <Space>
            <Button onClick={() => setViewModalVisible(false)}>关闭</Button>
          </Space>
        }
      >
        {viewingProject && (
          <div style={{ padding: '16px 0' }}>
            {/* 项目基本信息 */}
            <div style={{ marginBottom: '24px' }}>
              <h3 style={{ marginBottom: '16px', fontSize: '16px', fontWeight: 600 }}>
                基本信息
              </h3>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                <div>
                  <div style={{ marginBottom: '12px' }}>
                    <strong>项目名称：</strong>
                    <span>{viewingProject.name}</span>
                  </div>

                  <div style={{ marginBottom: '12px' }}>
                    <strong>状态：</strong>
                    <Tag
                      theme={viewingProject.status === 1 ? 'success' : viewingProject.status === 2 ? 'warning' : 'default'}
                      style={{ marginLeft: '8px' }}
                    >
                      {statusOptions.find(opt => opt.value === viewingProject.status)?.label || '未知'}
                    </Tag>
                  </div>


                </div>

                <div>
                  <div style={{ marginBottom: '12px' }}>
                    <strong>创建时间：</strong>
                    <span>{dayjs(Number(viewingProject.created_at) * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>
                  </div>

                  <div style={{ marginBottom: '12px' }}>
                    <strong>更新时间：</strong>
                    <span>{dayjs(Number(viewingProject.updated_at) * 1000).format('YYYY-MM-DD HH:mm:ss')}</span>
                  </div>


                </div>
              </div>

              {viewingProject.description && (
                <div style={{ marginTop: '16px' }}>
                  <strong>项目描述：</strong>
                  <div style={{
                    marginTop: '8px',
                    padding: '12px',
                    backgroundColor: '#f8f8f8',
                    borderRadius: '4px',
                    lineHeight: '1.6'
                  }}>
                    {viewingProject.description}
                  </div>
                </div>
              )}

              {viewingProject.thumbnail_path && (
                <div style={{ marginTop: '16px' }}>
                  <strong>缩略图：</strong>
                  <div style={{ marginTop: '8px' }}>
                    <FidImage fidOrUrl={viewingProject.thumbnail_path} width={200} height={150} style={{ borderRadius: 4 }} />
                  </div>
                </div>
              )}
            </div>

            {/* 关联的角色资产 */}
            <div>
              <h3 style={{ marginBottom: '16px', fontSize: '16px', fontWeight: 600 }}>
                关联的角色资产 ({viewingProject.assets?.length || 0})
              </h3>

              {viewingProject.assets && viewingProject.assets.length > 0 ? (
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))',
                  gap: '16px'
                }}>
                  {viewingProject.assets.map((asset: any) => (
                    <div
                      key={asset.id}
                      style={{
                        border: '1px solid #e7e7e7',
                        borderRadius: '6px',
                        padding: '16px',
                        backgroundColor: '#fff'
                      }}
                    >
                      {/* 资产缩略图 */}
                      <div style={{
                        textAlign: 'center',
                        marginBottom: '12px'
                      }}>
                        {asset.thumbnail_path ? (
                          <FidImage fidOrUrl={asset.thumbnail_path} width={'100%'} height={120} style={{ objectFit: 'cover', borderRadius: 4 }} />
                        ) : (
                          <div style={{
                            width: '100%',
                            height: '120px',
                            backgroundColor: '#f5f5f5',
                            borderRadius: '4px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: '#999'
                          }}>
                            暂无预览图
                          </div>
                        )}
                      </div>

                      {/* 资产信息 */}
                      <div style={{ fontSize: '14px' }}>
                        <div style={{
                          fontWeight: 500,
                          marginBottom: '8px',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}>
                          {asset.name}
                        </div>

                        <div style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          marginBottom: '8px'
                        }}>
                          <Tag size="small" variant="light">
                            角色模型
                          </Tag>
                          <span style={{ fontSize: '12px', color: '#666' }}>
                            {asset.source_file_size ?
                              `${(asset.source_file_size / 1024 / 1024).toFixed(2)} MB` :
                              '未知大小'
                            }
                          </span>
                        </div>

                        {/* 文件管理组件 */}
                        <div style={{ marginTop: '8px' }}>
                          <SourceFileManager
                            value={{
                              url: asset.source_file_url,
                              size: asset.source_file_size
                            }}
                            module="assets"
                            readOnly={true}
                            onChange={() => {}}
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div style={{
                  textAlign: 'center',
                  padding: '40px 0',
                  color: '#999',
                  backgroundColor: '#f8f8f8',
                  borderRadius: '6px'
                }}>
                  暂无关联的角色资产
                </div>
              )}
            </div>
          </div>
        )}
      </Dialog>
    </div>
  );
};

export default ProjectsPage;
