import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Space,
  Popconfirm,
  message,
  Dialog,
  Form,
  Select,
  Textarea,
  Pagination,
} from 'tdesign-react';
import FidImage from '../../../components/FidImage';
import { SearchIcon, AddIcon, EditIcon, DeleteIcon, BrowseIcon, DownloadIcon } from 'tdesign-icons-react';
import ThumbnailUpload from '../../../components/ThumbnailUpload';

import { AssetService, Asset, AssetCreateData } from '../../../services/assets';

import AssetSourceUploadDialog from '../../../components/AssetSourceUploadDialog';
import TransferManager from '../../../components/TransferManager';
import { useTransferManager } from '../../../hooks/useTransferManager';
import { instance } from '../../../utils/request';
import { FileUploadService } from '../../../services/fileUpload';

import { useAppSelector, useAppDispatch } from '../../../modules/store';
import { getUserInfo } from '../../../modules/user';
import dayjs from 'dayjs';

const { FormItem } = Form;
const { Option } = Select;

const AssetsPage: React.FC = () => {
  // 获取用户信息
  const dispatch = useAppDispatch();
  const userState = useAppSelector((state) => state.user);

  // 确保用户信息已加载
  useEffect(() => {
    if (!userState.userInfo || Object.keys(userState.userInfo).length === 0) {
      dispatch(getUserInfo());
    }
  }, [dispatch, userState.userInfo]);

  const [loading, setLoading] = useState(false);
  const [assets, setAssets] = useState<Asset[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  // 源文件上传对话框
  const [sourceDialogVisible, setSourceDialogVisible] = useState(false);
  const [createdAssetId, setCreatedAssetId] = useState<string>('');
  const [createdAssetName, setCreatedAssetName] = useState<string>('');

  // 断点续传相关状态
  const [resumeTaskId, setResumeTaskId] = useState<string>('');
  const [resumeFromChunk, setResumeFromChunk] = useState<number>(0);

  const [searchValue, setSearchValue] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [typeFilter, setTypeFilter] = useState<number[] | undefined>();

  // 传输管理器
  const transferManager = useTransferManager();

  // 检查资产是否有正在进行的上传任务
  const isAssetUploading = React.useCallback((assetId: string) => {
    if (!assetId) return false;

    // 检查传输管理器中是否有该资产的正在进行的上传任务
    const runningUploadTasks = transferManager.tasks.filter(task =>
      task.type === 'upload' &&
      task.assetId === assetId &&
      (task.status === 'running' || task.status === 'pending')
    );

    const isUploading = runningUploadTasks.length > 0;
    console.log(`[isAssetUploading] 检查资产 ${assetId}:`, {
      isUploading,
      runningTasksCount: runningUploadTasks.length,
      allUploadTasks: transferManager.tasks.filter(t => t.type === 'upload').map(t => ({ id: t.id, assetId: t.assetId, status: t.status }))
    });

    return isUploading;
  }, [transferManager.tasks]);

  // 处理恢复上传的回调
  const handleResumeUpload = React.useCallback((taskId: string, resumeFromChunk: number, assetId: string, assetName: string) => {
    setResumeTaskId(taskId);
    setResumeFromChunk(resumeFromChunk);
    setCreatedAssetId(assetId);
    setCreatedAssetName(assetName);
    setSourceDialogVisible(true);
  }, []);

  // 设置恢复上传回调
  React.useEffect(() => {
    transferManager.setResumeUploadCallback(handleResumeUpload);

    // 清理函数
    return () => {
      transferManager.setResumeUploadCallback(() => {});
    };
  }, [transferManager, handleResumeUpload]);

  // 在资产管理页面加载时显示传输管理器（默认最小化）
  React.useEffect(() => {
    transferManager.showTransferManager();
    // 通过在组件内部默认最小化来实现（TransferManager内部有最小化UI逻辑）
  }, []);

  // 弹窗状态
  const [modalVisible, setModalVisible] = useState(false);
  const [modalType, setModalType] = useState<'create' | 'edit'>('create');
  const [editingAsset, setEditingAsset] = useState<Asset | null>(null);
  const [form] = Form.useForm();
  // 监听字段值变化
  const thumbnailPath = Form.useWatch('thumbnail_path', form);
  // 本地选择的缩略图文件（避免 Form 未注册字段导致取不到）
  const thumbnailFileRef = React.useRef<File | null>(null);
  // 缩略图组件引用
  const thumbnailUploadRef = React.useRef<any>(null);

  // 查看弹窗状态
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [viewingAsset, setViewingAsset] = useState<Asset | null>(null);

  // 资产类型选项
  const assetTypeOptions = [
    { value: 1, label: '角色模型' },
    { value: 2, label: '场景模型' },
    { value: 3, label: '道具模型' },
    { value: 4, label: '音频文件' },
    { value: 5, label: '图片文件' },
    { value: 6, label: '其他' },
  ];

  // 状态选项已移除

  // 获取资产列表
  const fetchAssets = async () => {
    try {
      setLoading(true);

      const body: any = { page: currentPage, pageSize };

      // 构建条件数组，所有条件放在同一个条件组中形成AND关系
      const conditions: any[] = [];

      // 关键词
      if (searchValue && searchValue.trim()) {
        conditions.push({ field: 'name', op: 'LIKE', values: searchValue.trim() });
      }

      // 资产类型（多选，使用 | 拼接）
      if (typeFilter && typeFilter.length > 0) {
        conditions.push({ field: 'asset_type', op: 'EQ', values: typeFilter.join('|') });
      }

      // 默认只检索state为1的资产
      conditions.push({ field: 'state', op: 'EQ', values: '1' });

      // 将所有条件放在同一个条件组中
      if (conditions.length > 0) {
        body.searchConditionGroups = [{ conds: conditions }];
      }

      const response = await AssetService.searchAssets(body);
      setAssets(response.data || []);
      setTotal(response.total || 0);
    } catch (error) {
      message.error('获取资产列表失败');
      console.error('获取资产列表失败:', error);
    } finally {
      setLoading(false);
    }
  };



  useEffect(() => {
    fetchAssets();
  }, [currentPage, pageSize, searchValue, typeFilter]);

  // 当编辑资产改变时，更新表单值
  useEffect(() => {
    if (editingAsset && modalVisible && modalType === 'edit') {
      form.setFieldsValue({
        name: editingAsset.name,
        type_id: editingAsset.type_id,
        path: editingAsset.path,
        metadata: editingAsset.metadata,
        thumbnail_path: editingAsset.thumbnail_path,
        source_file_url: editingAsset.source_file_url,
        source_file_size: editingAsset.source_file_size,
      });
    }
  }, [editingAsset, modalVisible, modalType, form]);

  // 搜索
  const handleSearch = (value: string) => {
    setSearchValue(value);
    setCurrentPage(1);
  };

  // 重置搜索
  const handleReset = () => {
    setSearchValue('');
    setTypeFilter(undefined);
    setCurrentPage(1);
  };

  // 打开创建弹窗
  const handleCreate = () => {
    setModalType('create');
    setEditingAsset(null);
    form.reset();
    // 清空缩略图文件引用
    thumbnailFileRef.current = null;
    // 使用 setTimeout 确保表单重置后再设置默认值
    setTimeout(() => {
      form.setFieldsValue({
        type_id: 1,
      });
    }, 0);
    setModalVisible(true);
  };
  // 使用详情接口刷新查看/编辑数据
  const refreshAssetDetail = async (id: string) => {
    try {
      const a: any = await AssetService.getAssetById(id);
      const mapped: any = {
        id: a.id,
        name: a.name || '',
        type_id: mapAssetTypeToId(a.asset_type),
        metadata: a.data || '', // 详情页元数据栏映射 data 字段
        status: a.state === 1 ? 1 : 2,
        thumbnail_path: a.thumbnail_fid || a.belveth_fid || '',
        thumbnail_fid: a.thumbnail_fid || '',
        belveth_fid: a.belveth_fid || '',
        source_file_url: a.source_file_url || '',
        source_file_size: a.source_file_size || 0,
      };
      return mapped;
    } catch (e) {
      return null;
    }
  };

  // 本地映射方法（与服务层一致）
  function mapAssetTypeToId(t: any): number {
    const map: Record<string, number> = { ROLE: 1, MAP: 2, PROP: 3, AUDIO: 4, IMAGE: 5, OTHER: 6 };
    if (typeof t === 'number') return t;
    const s = String(t || '').trim();
    if (/^\d+$/.test(s)) return parseInt(s, 10);
    return map[s.toUpperCase()] || 0 as any;
  }


  // 打开查看弹窗
  const handleView = async (asset: Asset) => {
    const detail = await refreshAssetDetail(asset.id);
    setViewingAsset({ ...asset, ...(detail || {}) });
    setViewModalVisible(true);
  };

  // 删除功能已在下方定义

  // 打开编辑弹窗
  const handleEdit = async (asset: any) => {
    setModalType('edit');
    const detail = await refreshAssetDetail(asset.id);
    const merged = { ...asset, ...(detail || {}) };
    setEditingAsset(merged);
    // 先重置表单，然后设置值
    form.reset();
    // 清空缩略图文件引用
    thumbnailFileRef.current = null;

    setTimeout(() => {
      form.setFieldsValue({
        name: merged.name,
        type_id: merged.type_id,
        metadata: merged.metadata,
        status: merged.status,
        thumbnail_path: merged.thumbnail_path,
      });
    }, 0);
    setModalVisible(true);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const validateResult = await form.validate();
      if (validateResult === true) {
        const values = form.getFieldsValue(true);

        // 1) 若选择了本地缩略图文件，先走 single_file_upload 获取 fid（创建和编辑都需要）
        let thumbnailFid: string | undefined;
        const localThumbFile: File | undefined = thumbnailFileRef.current || undefined;
        if (localThumbFile) {
          try {
            // 计算 MD5 与大小
            const md5 = await FileUploadService.calculateFileMD5(localThumbFile);
            const size = localThumbFile.size;
            // 依据 fid 规范拼接 name 参数；若没有显式 fid，这里使用 md5 作为文件名以避免重复
            const base = ((instance.defaults as any)?.baseURL || '').replace(/\/$/, '');
            const url = new URL(base + '/single_file_upload');
            // 按要求硬编码 name=test/{fid}.jpg（注意：此处 {fid} 为字面量，不替换）
            url.searchParams.set('name', 'test/{fid}.jpg');
            url.searchParams.set('size', String(size));
            url.searchParams.set('h', md5);
            const res: any = await instance.request({
              url: url.toString(),
              method: 'PUT',
              data: localThumbFile,
              headers: { 'Content-Type': localThumbFile.type || 'application/octet-stream' },
            });
            const fid = res?.fid || res?.data?.fid || res?.data?.data?.fid || '';
            if (!fid) throw new Error('single_file_upload 未返回 fid');
            thumbnailFid = fid;
          } catch (e) {
            message.error((e as any)?.message || '缩略图上传失败');
            return;
          }
        }

        let recordId: string;

        if (modalType === 'create') {

          const createData: AssetCreateData = {
            name: values.name,
            type_id: values.type_id,
            metadata: values.metadata || '',
            thumbnail_fid: thumbnailFid || values.thumbnail_path || '',
            tags: [],
          };
          const createdAsset = await AssetService.create(createData);
          recordId = createdAsset.id;
          message.success('创建资产成功');
          // 打开源文件上传弹窗（后台任务）
          setCreatedAssetId(recordId);
          setCreatedAssetName(values.name);
          setSourceDialogVisible(true);
        } else {
          const updateBody: any = {
            asset_id: editingAsset!.id,
            name: values.name || '',
            data: values.metadata || '',
            project_id_list: [],
            tags: [],
            thumbnail_fid: thumbnailFid || values.thumbnail_path || '',
            asset_type: String(values.type_id || ''),
            belveth_fid: '',
            state: String(values.status || ''),
          };
          await instance.post('/update_asset', updateBody);
          recordId = editingAsset!.id;
          message.success('更新资产成功');
        }

        // 源文件上传已移至创建成功后的独立弹窗，此处不再处理文件上传任务

        setModalVisible(false);
        setEditingAsset(null); // 清除编辑状态
        form.reset(); // 重置表单
        // 延迟1秒调用fetchAssets
        setTimeout(() => {
          fetchAssets();
        }, 1000);

        
      }
    } catch (error) {
      message.error(modalType === 'create' ? '创建资产失败' : '更新资产失败');
      console.error('提交表单失败:', error);
    }
  };

  // 删除资产
  const handleDelete = async (id: string) => {
    try {
      await AssetService.delete(id);
      message.success('删除资产成功');
       // 延迟1秒调用fetchAssets
        setTimeout(() => {
          fetchAssets();
        }, 1000);
    } catch (error) {
      message.error('删除资产失败');
      console.error('删除资产失败:', error);
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的资产');
      return;
    }

    try {
      await Promise.all(selectedRowKeys.map(id => AssetService.delete(id)));
      message.success('批量删除成功');
      setSelectedRowKeys([]);
       // 延迟1秒调用fetchAssets
        setTimeout(() => {
          fetchAssets();
        }, 1000);
    } catch (error) {
      message.error('批量删除失败');
      console.error('批量删除失败:', error);
    }
  };

  // 表格列配置
  const columns = [
    {
      colKey: 'row-select',
      type: 'multiple' as const,
      width: 50,
    },
    {
      title: '缩略图',
      colKey: 'thumbnail_fid',
      width: 80,
      cell: ({ row }: { row: Asset }) => (
        <FidImage fidOrUrl={row.thumbnail_fid || row.thumbnail_path || ''} width={60} height={45} style={{ borderRadius: 4 }} />
      ),
    },
    {
      title: '资产名称',
      colKey: 'name',
      width: 200,
    },
    {
      title: '资产类型',
      colKey: 'type_name',
      width: 140,
      cell: ({ row }: { row: Asset }) => {
        const typeOption = assetTypeOptions.find(opt => opt.value === row.type_id);
        return typeOption?.label || '未知类别';
      },
    },

    // 状态列已移除

    {
      title: '创建者',
      colKey: 'created_by',
      width: 120,
      cell: ({ row }: { row: Asset & any }) => row.created_by || row.creator_id || '-',
    },
    {
      title: '创建时间',
      colKey: 'created_at',
      width: 180,
      cell: ({ row }: { row: Asset }) => {
        // 处理时间戳：如果是字符串数字，转换为数字后乘以1000（秒转毫秒）
        const timestamp = Number(row.created_at);
        return timestamp > 0 ? dayjs(timestamp * 1000).format('YYYY-MM-DD HH:mm:ss') : '-';
      },
    },
    {
      title: '操作',
      colKey: 'action',
      width: 200,
      cell: ({ row }: { row: Asset }) => (
        <Space>
          <Button
            size="small"
            variant="text"
            icon={<BrowseIcon />}
            onClick={() => handleView(row)}
          >
            查看
          </Button>
          <Button
            size="small"
            variant="text"
            icon={<EditIcon />}
            onClick={() => handleEdit(row)}
          >
            编辑
          </Button>
          <Popconfirm
            content="确定要删除这个资产吗？"
            onConfirm={() => handleDelete(row.id)}
          >
            <Button
              size="small"
              variant="text"
              theme="danger"
              icon={<DeleteIcon />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '16px' }}>
          <Space>
            <Input
              placeholder="搜索资产名称"
              value={searchValue}
              onChange={(value) => setSearchValue(value)}
              onEnter={() => handleSearch(searchValue)}
              style={{ width: '200px' }}
              suffixIcon={<SearchIcon />}
            />
            <Select
              placeholder="资产类型"
              value={typeFilter}
              multiple
              onChange={(value) => setTypeFilter(value as number[])}
              clearable
              style={{ width: '160px' }}
            >
              {assetTypeOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
            {/* 状态筛选已移除 */}
            <Button onClick={() => handleSearch(searchValue)} icon={<SearchIcon />}>
              搜索
            </Button>
            <Button onClick={handleReset}>
              重置
            </Button>
          </Space>
        </div>

        <div style={{ marginBottom: '16px' }}>
          <Space>
            <Button theme="primary" onClick={handleCreate} icon={<AddIcon />}>
              新建资产
            </Button>
            <Popconfirm
              content={`确定要删除选中的 ${selectedRowKeys.length} 个资产吗？`}
              onConfirm={handleBatchDelete}
            >
              <Button
                theme="danger"
                disabled={selectedRowKeys.length === 0}
                icon={<DeleteIcon />}
              >
                批量删除 ({selectedRowKeys.length})
              </Button>
            </Popconfirm>
          </Space>
        </div>

        {/* 表格 */}
        <Table
          data={assets}
          columns={columns}
          loading={loading}
          rowKey="id"
          selectedRowKeys={selectedRowKeys}
          onSelectChange={(selectedRowKeys: (string | number)[]) => {
            setSelectedRowKeys(selectedRowKeys as string[]);
          }}
        />

        {/* 分页 */}
        <div style={{ marginTop: '16px', textAlign: 'right' }}>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={total}
            onChange={(pageInfo: { current: number; pageSize: number }) => {
              setCurrentPage(pageInfo.current);
              setPageSize(pageInfo.pageSize);
            }}
          />
          <div style={{ marginTop: '8px', fontSize: '14px', color: '#666' }}>
            共 {total} 条记录
          </div>
        </div>
      </Card>

      {/* 创建/编辑弹窗 */}
      <Dialog
        header={modalType === 'create' ? '新建资产' : '编辑资产'}
        visible={modalVisible}
        onCancel={() => {
          console.log('Asset Dialog onCancel triggered');
          // 重置表单和缩略图
          form.reset();
          thumbnailFileRef.current = null;
          if (thumbnailUploadRef.current?.reset) {
            thumbnailUploadRef.current.reset();
          }
          setModalVisible(false);
        }}
        onClose={() => {
          console.log('Asset Dialog onClose triggered');
          // 重置表单和缩略图
          form.reset();
          thumbnailFileRef.current = null;
          if (thumbnailUploadRef.current?.reset) {
            thumbnailUploadRef.current.reset();
          }
          setModalVisible(false);
        }}
        width={600}
        closeBtn={true}
        closeOnEscKeydown={true}
        closeOnOverlayClick={true}
        footer={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              {modalType === 'edit' && editingAsset && (
                <Button
                  theme="primary"
                  variant="outline"
                  onClick={() => {
                    // 复用创建资产时的源文件上传逻辑
                    setCreatedAssetId(editingAsset.id);
                    setCreatedAssetName(editingAsset.name || '未知资产');
                    setSourceDialogVisible(true);
                  }}
                >
                  {editingAsset.belveth_fid ? '重新上传源文件' : '上传源文件'}
                </Button>
              )}
            </div>
            <Space>
              <Button onClick={() => {
                // 重置表单和缩略图
                form.reset();
                thumbnailFileRef.current = null;
                if (thumbnailUploadRef.current?.reset) {
                  thumbnailUploadRef.current.reset();
                }
                setModalVisible(false);
              }}>取消</Button>
              <Button theme="primary" onClick={handleSubmit}>
                {modalType === 'create' ? '创建' : '保存'}
              </Button>
            </Space>
          </div>
        }
      >
        <Form form={form} layout="vertical">
          <FormItem
            label="资产名称"
            name="name"
            rules={[{ required: true, message: '请输入资产名称' }]}
          >
            <Input placeholder="请输入资产名称" />
          </FormItem>

          <FormItem
            label="资产类型"
            name="type_id"
            rules={[{ required: true, message: '请选择资产类型' }]}
          >
            <Select placeholder="请选择资产类型">
              {assetTypeOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </FormItem>



          {/* 创建者ID字段已移除 */}
          {/* 源文件管理字段已移除，将在创建成功后的新弹窗中进行 */}

          <FormItem label="缩略图" name="thumbnail_path">
            <ThumbnailUpload
              ref={thumbnailUploadRef}
              value={thumbnailPath as string}
              onChange={(value, file) => {
                // 只用于展示路径（fid 或 URL），以及缓存本地文件以供提交阶段上传
                form.setFieldsValue({ thumbnail_path: value });
                thumbnailFileRef.current = file || null;
              }}
              previewOnly
            />
          </FormItem>



          {/* 状态字段已移除 */}

          <FormItem label="元数据" name="metadata">
            <Textarea
              placeholder="请输入JSON格式的元数据"
              rows={4}
            />
          </FormItem>
        </Form>
      </Dialog>

      {/* 查看详情弹窗 */}
      <Dialog
        header="资产详情"
        visible={viewModalVisible}
        onCancel={() => setViewModalVisible(false)}
        onClose={() => setViewModalVisible(false)}
        width={600}
        closeBtn={true}
        closeOnEscKeydown={true}
        closeOnOverlayClick={true}
        footer={
          <Button onClick={() => setViewModalVisible(false)}>关闭</Button>
        }
      >
        {viewingAsset && (
          <div style={{ padding: '16px 0' }}>
            <div style={{ marginBottom: '16px' }}>
              <strong>资产名称：</strong>
              <span>{viewingAsset.name}</span>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>资产类型：</strong>
              <span>
                {(() => {
                  const typeMap = {
                    1: '角色模型',
                    2: '场景模型',
                    3: '道具模型',
                    4: '音频文件',
                    5: '图片文件',
                    6: '其他',
                  } as Record<number, string>;
                  return typeMap[Number(viewingAsset.type_id)] || '未知类别';
                })()}
              </span>
            </div>

            <div style={{ marginBottom: '16px' }}>
              <strong>缩略图：</strong>
              <div style={{ marginTop: '8px' }}>
                {viewingAsset.thumbnail_path ? (
                  <FidImage fidOrUrl={viewingAsset.thumbnail_path} width={120} height={90} style={{ borderRadius: 4 }} />
                ) : (
                  <div style={{
                    width: '120px',
                    height: '90px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: '#f5f5f5',
                    borderRadius: '4px',
                    fontSize: '12px',
                    color: '#999'
                  }}>
                    暂无缩略图
                  </div>
                )}
              </div>
            </div>
            {/* 状态字段已移除 */}
            <div style={{ marginBottom: '16px' }}>
              <strong>源文件：</strong>
              <div style={{ marginTop: '8px' }}>
                {viewingAsset.belveth_fid ? (
                  <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                    <span style={{ fontSize: '14px', color: '#333' }}>
                      {viewingAsset.name || '未知文件'}
                    </span>
                    <Button
                      theme="primary"
                      variant="outline"
                      size="small"
                      icon={<DownloadIcon />}
                      onClick={() => {
                        // 启动传输管理器下载任务
                        transferManager.startDownloadTask(
                          viewingAsset.belveth_filename || viewingAsset.name || '未知文件',
                          0, // 文件大小暂时设为0，实际下载时会更新
                          viewingAsset.belveth_fid,
                          viewingAsset.belveth_filename // 传递完整文件名用于下载URL的name参数
                        );
                      }}
                    >
                      下载文件
                    </Button>
                  </div>
                ) : (
                  <span style={{ color: '#999' }}>未上传源文件</span>
                )}
              </div>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>元数据：</strong>
              <pre style={{
                background: '#f5f5f5',
                padding: '12px',
                borderRadius: '4px',
                fontSize: '12px',
                lineHeight: '1.4',
                overflow: 'auto',
                maxHeight: '200px'
              }}>
                {typeof viewingAsset.metadata === 'object'
                  ? JSON.stringify(viewingAsset.metadata, null, 2)
                  : viewingAsset.metadata || '无'}
              </pre>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>创建时间：</strong>
              <span>{
                viewingAsset.created_at && Number(viewingAsset.created_at) > 0
                  ? dayjs(Number(viewingAsset.created_at) * 1000).format('YYYY-MM-DD HH:mm:ss')
                  : '-'
              }</span>
            </div>
            <div>
              <strong>更新时间：</strong>
              <span>{
                viewingAsset.updated_at && Number(viewingAsset.updated_at) > 0
                  ? dayjs(Number(viewingAsset.updated_at) * 1000).format('YYYY-MM-DD HH:mm:ss')
                  : '-'
              }</span>
            </div>
          </div>
        )}
      </Dialog>
      {/* 原上传任务悬浮窗已移除，使用传输管理器替代 */}

      {/* 源文件上传对话框 */}
      <AssetSourceUploadDialog
        visible={sourceDialogVisible}
        assetId={createdAssetId}
        assetName={createdAssetName}
        onClose={() => setSourceDialogVisible(false)}
        onStartUpload={(fileName, fileSize, totalChunks) => transferManager.startUploadTask(fileName, fileSize, createdAssetId, totalChunks)}
        onUpdateProgress={(taskId, progress, transferredSize, speed) => {
          transferManager.updateTask(taskId, {
            status: 'running',
            progress,
            transferredSize,
            speed,
          });
        }}
        onUpdateJobId={(taskId, jobId) => {
          transferManager.updateTask(taskId, { jobId });
        }}
        onUpdateTaskInfo={(taskId, fileName, fileSize, totalChunks) => {
          // 重试模式下更新任务的基本信息
          transferManager.updateTask(taskId, {
            name: fileName,
            size: fileSize,
            totalChunks: totalChunks,
            // 重置进度相关信息
            progress: 0,
            transferredSize: 0,
            speed: undefined,
          });
          console.log(`[Assets] 更新任务信息: ${taskId}, ${fileName}, ${fileSize}, ${totalChunks}`);
        }}
        onTaskComplete={(taskId) => {
          transferManager.updateTask(taskId, {
            status: 'completed',
            progress: 100,
            completedAt: new Date(),
            speed: undefined,
          });
        }}
        onTaskError={(taskId, error) => {
          transferManager.updateTask(taskId, {
            status: 'failed',
            error,
          });
        }}
        onRegisterController={(taskId, abortController) => {
          transferManager.registerUploadController(taskId, abortController);
        }}
        resumeTaskId={resumeTaskId}
        resumeFromChunk={resumeFromChunk}
        checkAssetUploading={isAssetUploading}
        canCreateNewUploadTask={transferManager.canCreateNewUploadTask}
        getRunningUploadCount={transferManager.getRunningUploadCount}
        maxConcurrentUploads={transferManager.maxConcurrentUploads}
      />

      {/* 传输管理器 - 常驻显示 */}
      <TransferManager
        visible={transferManager.isVisible}
        onClose={() => {}}
        tasks={transferManager.tasks}
        onTaskAction={transferManager.handleTaskAction}
        onClearCompleted={transferManager.clearCompleted}
        onClearFailed={transferManager.clearFailed}
        onPauseAll={transferManager.pauseAllTasks}
        onResumeAll={transferManager.resumeAllTasks}
        onRetryFailed={transferManager.retryFailedTasks}
        minimizedByDefault
        maxConcurrentUploads={transferManager.maxConcurrentUploads}
      />
    </div>
  );
};

export default AssetsPage;
