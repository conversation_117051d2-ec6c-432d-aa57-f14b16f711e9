import React, { useState, useEffect } from 'react';
import { buildThumbnailUrl } from '../../../utils/url';
import {
  Card,
  Table,
  Button,
  Input,
  Space,
  Tag,
  Popconfirm,
  message,
  Dialog,
  Form,
  Select,
  Textarea,
  Pagination,
  Image,
} from 'tdesign-react';
import { SearchIcon, AddIcon, EditIcon, DeleteIcon } from 'tdesign-icons-react';
import ThumbnailUpload from '../../../components/ThumbnailUpload';
import { StoryboardService, Storyboard, StoryboardCreateData, StoryboardUpdateData } from '../../../services/storyboards';
import { ProjectService, Project } from '../../../services/projects';
import dayjs from 'dayjs';

const { FormItem } = Form;
const { Option } = Select;

const StoryboardsPage: React.FC = () => {

  const [loading, setLoading] = useState(false);
  const [storyboards, setStoryboards] = useState<Storyboard[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchValue, setSearchValue] = useState('');
  const [projectFilter, setProjectFilter] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  
  // 弹窗状态
  const [modalVisible, setModalVisible] = useState(false);
  const [modalType, setModalType] = useState<'create' | 'edit'>('create');
  const [editingStoryboard, setEditingStoryboard] = useState<Storyboard | null>(null);
  const [form] = Form.useForm();

  // 监听缩略图字段值变化
  const thumbnailPath = Form.useWatch('thumbnail_path', form);

  // 状态选项
  const statusOptions = [
    { label: '禁用', value: 0 },
    { label: '启用', value: 1 },
    { label: '草稿', value: 2 },
  ];

  // 获取项目列表（用于下拉选择）
  const fetchProjects = async () => {
    try {
      const response = await ProjectService.getList({ limit: 100 });
      setProjects(response.list);
    } catch (error) {
      console.error('获取项目列表失败:', error);
    }
  };

  // 获取故事板列表
  const fetchStoryboards = async () => {
    setLoading(true);
    try {
      const response = await StoryboardService.getList({
        page: currentPage,
        limit: pageSize,
        search: searchValue,
        projectId: projectFilter,
      });
      setStoryboards(response.list);
      setTotal(response.pagination.total);
    } catch (error) {
      message.error('获取故事板列表失败');
      console.error('获取故事板列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProjects();
  }, []);

  useEffect(() => {
    fetchStoryboards();
  }, [currentPage, pageSize, searchValue, projectFilter]);

  // 当编辑故事板改变时，更新表单值
  useEffect(() => {
    if (editingStoryboard && modalVisible && modalType === 'edit') {
      form.setFieldsValue({
        project_id: editingStoryboard.project_id,
        name: editingStoryboard.name,
        description: editingStoryboard.description,
        status: editingStoryboard.status,
        thumbnail_path: editingStoryboard.thumbnail_path,
        content: editingStoryboard.content ? JSON.stringify(editingStoryboard.content, null, 2) : '',
      });
    }
  }, [editingStoryboard, modalVisible, modalType, form]);

  // 搜索
  const handleSearch = (value: string) => {
    setSearchValue(value);
    setCurrentPage(1);
  };

  // 重置搜索
  const handleReset = () => {
    setSearchValue('');
    setProjectFilter('');
    setCurrentPage(1);
  };

  // 打开创建弹窗
  const handleCreate = () => {
    setModalType('create');
    setEditingStoryboard(null);
    form.reset();
    form.setFieldsValue({
      status: 1,
      duration_seconds: 0,
      project_scene_id: 'default_scene', // 默认场景ID
    });
    setModalVisible(true);
  };

  // 打开编辑弹窗
  const handleEdit = (storyboard: Storyboard) => {
    setModalType('edit');
    setEditingStoryboard(storyboard);
    // 先重置表单，然后设置值
    form.reset();
    setTimeout(() => {
      form.setFieldsValue({
        project_id: storyboard.project_id,
        name: storyboard.name,
        description: storyboard.description,
        status: storyboard.status,
        thumbnail_path: storyboard.thumbnail_path,
        content: storyboard.content ? JSON.stringify(storyboard.content, null, 2) : '',
      });
    }, 0);
    setModalVisible(true);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const validateResult = await form.validate();
      if (validateResult === true) {
        const values = form.getFieldsValue(true);

        // 处理content字段
        let content = null;
        if (values.content) {
          try {
            content = JSON.parse(values.content);
          } catch (e) {
            message.error('内容格式不正确，请输入有效的JSON格式');
            return;
          }
        }

        if (modalType === 'create') {
          const createData: StoryboardCreateData = {
            project_id: values.project_id,
            name: values.name,
            description: values.description,
            content,
            thumbnail_path: values.thumbnail_path,
            status: values.status,
          };
          await StoryboardService.create(createData);
          message.success('创建故事板成功');
        } else {
          const updateData: StoryboardUpdateData = {
            name: values.name,
            description: values.description,
            content,
            thumbnail_path: values.thumbnail_path,
            status: values.status,
          };
          await StoryboardService.update(editingStoryboard!.id, updateData);
          message.success('更新故事板成功');
        }

        setModalVisible(false);
        fetchStoryboards();
      }
    } catch (error) {
      message.error(modalType === 'create' ? '创建故事板失败' : '更新故事板失败');
      console.error('提交表单失败:', error);
    }
  };

  // 删除故事板
  const handleDelete = async (id: string) => {
    try {
      await StoryboardService.delete(id);
      message.success('删除故事板成功');
      fetchStoryboards();
    } catch (error) {
      message.error('删除故事板失败');
      console.error('删除故事板失败:', error);
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的故事板');
      return;
    }
    
    try {
      await StoryboardService.batchDelete(selectedRowKeys);
      message.success('批量删除成功');
      setSelectedRowKeys([]);
      fetchStoryboards();
    } catch (error) {
      message.error('批量删除失败');
      console.error('批量删除失败:', error);
    }
  };

  // 表格列配置
  const columns = [
    {
      colKey: 'row-select',
      type: 'multiple' as const,
      width: 50,
    },
    {
      title: '缩略图',
      colKey: 'thumbnail_path',
      width: 80,
      cell: ({ row }: { row: Storyboard }) => {
        const imageUrl = row.thumbnail_path
          ? (buildThumbnailUrl(row.thumbnail_path))
          : '/images/default-thumbnail.svg';
        return (
          <Image
            src={imageUrl}
            style={{ width: '60px', height: '45px', borderRadius: '4px' }}
            fit="cover"
            error={
              <div style={{
                width: '60px',
                height: '45px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#f5f5f5',
                borderRadius: '4px',
                fontSize: '12px',
                color: '#999'
              }}>
                无图片
              </div>
            }
          />
        );
      },
    },
    {
      title: '故事板名称',
      colKey: 'name',
      width: 200,
    },
    {
      title: '所属项目',
      colKey: 'project_name',
      width: 150,
      cell: ({ row }: { row: Storyboard }) => row.project_name || '-',
    },
    {
      title: '描述',
      colKey: 'description',
      width: 250,
      cell: ({ row }: { row: Storyboard }) => row.description || '-',
    },
    {
      title: '状态',
      colKey: 'status',
      width: 100,
      cell: ({ row }: { row: Storyboard }) => {
        const statusMap = {
          0: { label: '禁用', color: 'default' },
          1: { label: '启用', color: 'success' },
          2: { label: '草稿', color: 'warning' },
        };
        const statusInfo = statusMap[row.status as keyof typeof statusMap] || { label: '未知', color: 'default' };
        return <Tag color={statusInfo.color}>{statusInfo.label}</Tag>;
      },
    },
    {
      title: '创建时间',
      colKey: 'created_at',
      width: 180,
      cell: ({ row }: { row: Storyboard }) => dayjs(row.created_at).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      colKey: 'action',
      width: 150,
      cell: ({ row }: { row: Storyboard }) => (
        <Space>
          <Button
            size="small"
            variant="text"
            icon={<EditIcon />}
            onClick={() => handleEdit(row)}
          >
            编辑
          </Button>
          <Popconfirm
            content="确定要删除这个故事板吗？"
            onConfirm={() => handleDelete(row.id)}
          >
            <Button
              size="small"
              variant="text"
              theme="danger"
              icon={<DeleteIcon />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card title="故事板管理" bordered={false}>
        {/* 搜索栏 */}
        <div style={{ marginBottom: '16px' }}>
          <Space>
            <Input
              placeholder="搜索故事板名称或描述"
              value={searchValue}
              onChange={(value) => setSearchValue(value)}
              onEnter={() => handleSearch(searchValue)}
              style={{ width: '300px' }}
              suffixIcon={<SearchIcon />}
            />
            <Select
              placeholder="选择项目筛选"
              value={projectFilter}
              onChange={(value) => setProjectFilter(value as string)}
              style={{ width: '200px' }}
              clearable
            >
              {projects.map(project => (
                <Option key={project.id} value={project.id}>
                  {project.name}
                </Option>
              ))}
            </Select>
            <Button onClick={() => handleSearch(searchValue)}>搜索</Button>
            <Button variant="outline" onClick={handleReset}>重置</Button>
          </Space>
        </div>

        {/* 操作栏 */}
        <div style={{ marginBottom: '16px' }}>
          <Space>
            <Button theme="primary" icon={<AddIcon />} onClick={handleCreate}>
              新建故事板
            </Button>
            <Popconfirm
              content="确定要批量删除选中的故事板吗？"
              onConfirm={handleBatchDelete}
            >
              <Button
                theme="danger"
                variant="outline"
                disabled={selectedRowKeys.length === 0}
              >
                批量删除 ({selectedRowKeys.length})
              </Button>
            </Popconfirm>
          </Space>
        </div>

        {/* 表格 */}
        <Table
          data={storyboards}
          columns={columns}
          loading={loading}
          rowKey="id"
          selectedRowKeys={selectedRowKeys}
          onSelectChange={(selectedRowKeys: (string | number)[]) => {
            setSelectedRowKeys(selectedRowKeys as string[]);
          }}
        />

        {/* 分页 */}
        <div style={{ marginTop: '16px', textAlign: 'right' }}>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={total}
            onChange={(pageInfo: { current: number; pageSize: number }) => {
              setCurrentPage(pageInfo.current);
              setPageSize(pageInfo.pageSize);
            }}
          />
          <div style={{ marginTop: '8px', fontSize: '14px', color: '#666' }}>
            共 {total} 条记录
          </div>
        </div>
      </Card>

      {/* 创建/编辑弹窗 */}
      <Dialog
        header={modalType === 'create' ? '新建故事板' : '编辑故事板'}
        visible={modalVisible}
        onCancel={() => {
          console.log('Storyboard Dialog onCancel triggered');
          setModalVisible(false);
        }}
        onClose={() => {
          console.log('Storyboard Dialog onClose triggered');
          setModalVisible(false);
        }}
        width={800}
        closeBtn={true}
        closeOnEscKeydown={true}
        closeOnOverlayClick={true}
        footer={
          <Space>
            <Button onClick={() => setModalVisible(false)}>取消</Button>
            <Button theme="primary" onClick={handleSubmit}>
              {modalType === 'create' ? '创建' : '保存'}
            </Button>
          </Space>
        }
      >
        <Form form={form} layout="vertical">
          <FormItem
            label="故事板名称"
            name="name"
            rules={[{ required: true, message: '请输入故事板名称' }]}
          >
            <Input placeholder="请输入故事板名称" />
          </FormItem>
          
          {modalType === 'create' && (
            <>
              <FormItem
                label="所属项目"
                name="assets"
                rules={[{ required: true, message: '请选择所属项目' }]}
              >
                <Select placeholder="请选择所属项目">
                  {projects.map(project => (
                    <Option key={project.id} value={project.id}>
                      {project.name}
                    </Option>
                  ))}
                </Select>
              </FormItem>
              
            </>
          )}

          <FormItem label="故事板描述" name="description">
            <Textarea placeholder="请输入故事板描述" rows={3} />
          </FormItem>

          <FormItem label="状态" name="status">
            <Select placeholder="请选择状态">
              {statusOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </FormItem>

          <FormItem label="缩略图" name="thumbnail_path">
            <ThumbnailUpload
              value={thumbnailPath as string}
              onChange={(value) => form.setFieldsValue({ thumbnail_path: value })}
            />
          </FormItem>
          
          <FormItem label="内容(JSON格式)" name="content">
            <Textarea 
              placeholder="请输入JSON格式的内容，例如：{&quot;scenes&quot;: [], &quot;characters&quot;: []}" 
              rows={4} 
            />
          </FormItem>
        </Form>
      </Dialog>
    </div>
  );
};

export default StoryboardsPage;
