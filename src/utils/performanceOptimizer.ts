/**
 * 性能优化工具集
 * 基于 Chrome DevTools 分析结果的优化方案
 */

// 1. 任务调度器 - 解决主线程阻塞问题
export class TaskScheduler {
  private taskQueue: Array<() => void> = [];
  private isRunning = false;

  // 将大任务分解为小任务
  scheduleTask(task: () => void) {
    this.taskQueue.push(task);
    if (!this.isRunning) {
      this.runTasks();
    }
  }

  private async runTasks() {
    this.isRunning = true;
    
    while (this.taskQueue.length > 0) {
      const task = this.taskQueue.shift();
      if (task) {
        task();
        
        // 让出主线程控制权，避免长任务阻塞
        await new Promise(resolve => setTimeout(resolve, 0));
      }
    }
    
    this.isRunning = false;
  }

  // 分块处理大数据集
  static async processLargeDataset<T>(
    data: T[],
    processor: (item: T) => void,
    chunkSize: number = 100
  ) {
    for (let i = 0; i < data.length; i += chunkSize) {
      const chunk = data.slice(i, i + chunkSize);
      
      chunk.forEach(processor);
      
      // 每处理一个块后让出控制权
      await new Promise(resolve => setTimeout(resolve, 0));
    }
  }
}

// 2. 组件渲染优化工具
export const RenderOptimizer = {
  // 防抖函数 - 减少频繁更新
  debounce<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let timeoutId: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), delay);
    };
  },

  // 节流函数 - 限制执行频率
  throttle<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let lastCall = 0;
    
    return (...args: Parameters<T>) => {
      const now = Date.now();
      if (now - lastCall >= delay) {
        lastCall = now;
        func(...args);
      }
    };
  },

  // 虚拟滚动优化
  calculateVisibleItems(
    containerHeight: number,
    itemHeight: number,
    scrollTop: number,
    totalItems: number,
    buffer: number = 5
  ) {
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - buffer);
    const endIndex = Math.min(totalItems - 1, startIndex + visibleCount + buffer * 2);
    
    return { startIndex, endIndex, visibleCount };
  }
};

// 3. 内存优化工具
export class MemoryOptimizer {
  private static cache = new Map<string, any>();
  private static maxCacheSize = 100;

  // LRU 缓存实现
  static setCache(key: string, value: any) {
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, value);
  }

  static getCache(key: string) {
    const value = this.cache.get(key);
    if (value !== undefined) {
      // 重新设置以更新 LRU 顺序
      this.cache.delete(key);
      this.cache.set(key, value);
    }
    return value;
  }

  // 清理未使用的缓存
  static clearCache() {
    this.cache.clear();
  }

  // 内存使用监控
  static getMemoryUsage() {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
        limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024),
      };
    }
    return null;
  }
}

// 4. 网络请求优化
export class NetworkOptimizer {
  private static requestCache = new Map<string, Promise<any>>();

  // 请求去重 - 避免重复请求
  static async deduplicateRequest<T>(
    key: string,
    requestFn: () => Promise<T>
  ): Promise<T> {
    if (this.requestCache.has(key)) {
      return this.requestCache.get(key);
    }

    const promise = requestFn().finally(() => {
      // 请求完成后清除缓存
      this.requestCache.delete(key);
    });

    this.requestCache.set(key, promise);
    return promise;
  }

  // 批量请求优化
  static createBatchProcessor<T, R>(
    batchFn: (items: T[]) => Promise<R[]>,
    delay: number = 50,
    maxBatchSize: number = 10
  ) {
    let batch: T[] = [];
    let resolvers: Array<(value: R) => void> = [];
    let timer: NodeJS.Timeout | null = null;

    const processBatch = async () => {
      if (batch.length === 0) return;

      const currentBatch = [...batch];
      const currentResolvers = [...resolvers];
      
      batch = [];
      resolvers = [];
      timer = null;

      try {
        const results = await batchFn(currentBatch);
        results.forEach((result, index) => {
          currentResolvers[index]?.(result);
        });
      } catch (error) {
        // 处理批量请求错误
        console.error('Batch request failed:', error);
      }
    };

    return (item: T): Promise<R> => {
      return new Promise((resolve) => {
        batch.push(item);
        resolvers.push(resolve);

        if (batch.length >= maxBatchSize) {
          // 达到最大批次大小，立即处理
          if (timer) {
            clearTimeout(timer);
            timer = null;
          }
          processBatch();
        } else if (!timer) {
          // 设置延迟处理
          timer = setTimeout(processBatch, delay);
        }
      });
    };
  }
}

// 5. 图片优化工具
export const ImageOptimizer = {
  // 懒加载图片
  createLazyImageObserver(callback: (entry: IntersectionObserverEntry) => void) {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(callback);
      },
      {
        rootMargin: '50px 0px',
        threshold: 0.01,
      }
    );

    return observer;
  },

  // 图片预加载
  preloadImage(src: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve();
      img.onerror = reject;
      img.src = src;
    });
  },

  // 批量预加载图片
  async preloadImages(srcs: string[], concurrency: number = 3) {
    const chunks = [];
    for (let i = 0; i < srcs.length; i += concurrency) {
      chunks.push(srcs.slice(i, i + concurrency));
    }

    for (const chunk of chunks) {
      await Promise.all(chunk.map(src => this.preloadImage(src)));
    }
  }
};

// 6. 性能监控和报告
export class PerformanceReporter {
  // 长任务监控
  static observeLongTasks(callback: (entry: PerformanceEntry) => void) {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach(callback);
      });

      try {
        observer.observe({ entryTypes: ['longtask'] });
        return observer;
      } catch (error) {
        console.warn('Long task observer not supported');
      }
    }
    return null;
  }

  // 生成性能报告
  static generatePerformanceReport() {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const paint = performance.getEntriesByType('paint');
    
    const report = {
      // 页面加载时间
      pageLoadTime: navigation.loadEventEnd - navigation.navigationStart,
      // DOM 内容加载时间
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.navigationStart,
      // 首次内容绘制
      firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
      // 内存使用情况
      memoryUsage: MemoryOptimizer.getMemoryUsage(),
      // 资源加载统计
      resourceStats: this.getResourceStats(),
    };

    return report;
  }

  private static getResourceStats() {
    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    
    const stats = {
      total: resources.length,
      byType: {} as Record<string, number>,
      slowResources: [] as Array<{ name: string; duration: number }>,
    };

    resources.forEach(resource => {
      // 按类型统计
      const type = resource.initiatorType || 'other';
      stats.byType[type] = (stats.byType[type] || 0) + 1;

      // 找出加载慢的资源
      if (resource.duration > 1000) {
        stats.slowResources.push({
          name: resource.name,
          duration: resource.duration,
        });
      }
    });

    return stats;
  }
}

// 导出全局实例
export const taskScheduler = new TaskScheduler();
export const memoryOptimizer = MemoryOptimizer;
export const networkOptimizer = NetworkOptimizer;
