import axios from 'axios';
import proxy from '../configs/host';

const env = import.meta.env.MODE || 'development';

// 添加fallback逻辑，确保即使env不匹配也有默认值
const getApiHost = () => {
  const envConfig = proxy[env];
  if (envConfig && envConfig.API) {
    return envConfig.API;
  }

  // 如果当前环境配置不存在，根据运行环境决定fallback
  // 使用typeof window检查是否在浏览器环境
  if (typeof window !== 'undefined' && window.location && window.location.port === '8082') {
    return '/api';
  }

  // 开发环境fallback
  return 'http://localhost:3002/api';
};

const API_HOST = getApiHost();

const SUCCESS_CODE = 0;
const TIMEOUT = 30000;

// 保存当前授权 token（去掉 Bearer 前缀）
let currentAuth = '';
const AUTH_STORAGE_KEY = '__TVI_AUTH__';
// 启动时尝试从 sessionStorage 恢复
try {
  if (typeof window !== 'undefined' && window.sessionStorage) {
    const cached = window.sessionStorage.getItem(AUTH_STORAGE_KEY) || '';
    if (cached) currentAuth = cached;
  }
} catch {}
// 对外暴露当前授权，供获取用户信息等处使用
export const getCurrentAuth = () => currentAuth;
export const setCurrentAuth = (rawOrBearer: string) => {
  if (!rawOrBearer) return;
  currentAuth = rawOrBearer.replace(/^Bearer\s+/i, '');
  try {
    if (typeof window !== 'undefined' && window.sessionStorage) {
      window.sessionStorage.setItem(AUTH_STORAGE_KEY, currentAuth);
    }
  } catch {}
};
export const clearStoredAuth = () => {
  try {
    if (typeof window !== 'undefined' && window.sessionStorage) {
      window.sessionStorage.removeItem(AUTH_STORAGE_KEY);
    }
  } catch {}
  currentAuth = '';
};
// 注册 auth 回调，随登录态变化更新 Authorization（动态导入以兼容 Node16 模块解析）
(async () => {
  try {
    const tvi = (await import('@tencent/tvi-login')).default as any;
    tvi?.oauth?.auth?.((_: any, value: string) => setCurrentAuth(value));
  } catch (e) {
    // 忽略 SDK 未就绪时的异常
  }
})();



export const instance = axios.create({
  baseURL: API_HOST,
  timeout: TIMEOUT,
  withCredentials: true,
});

// 请求拦截器：仅在已有 token 时注入 Authorization，不做额外兜底
instance.interceptors.request.use(async (config) => {
  if (currentAuth) {
    config.headers = config.headers || {};
    (config.headers as any).Authorization = `Bearer ${currentAuth}`;
  }
  return config;
});

instance.interceptors.response.use(
  // eslint-disable-next-line consistent-return
  (response) => {
    if (response.status === 200) {
      const { data } = response;
      // 兼容两种格式：{ code: 0, data: ... } 或 { rsp: { code: 0 }, project: [...] }
      if (typeof data === 'object') {
        const code = (data as any)?.code ?? (data as any)?.rsp?.code;
        if (code === SUCCESS_CODE || code === undefined) {
          return data;
        }
      }
      return Promise.reject(data);
    }
    return Promise.reject(response?.data);
  },
  (e) => Promise.reject(e),
);

export default instance;
