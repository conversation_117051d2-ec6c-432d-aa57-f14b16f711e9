/**
 * URL工具函数
 * 根据环境自动检测并返回正确的基础URL
 */

// 检测当前运行环境
const detectEnvironment = () => {
  // Docker环境检测（通过端口判断）
  if (window.location.port === '8082') {
    return 'docker';
  }
  
  // 开发环境检测
  if (window.location.hostname === 'localhost' && window.location.port === '3001') {
    return 'development';
  }
  
  // 生产环境
  return 'production';
};

// 获取基础URL
const getBaseUrl = () => {
  const env = detectEnvironment();
  
  switch (env) {
    case 'docker':
      // Docker环境使用当前域名（通过Nginx代理）
      return window.location.origin;
    case 'development':
      // 开发环境直接连接后端
      return 'http://localhost:3002';
    default:
      // 生产环境使用当前域名
      return window.location.origin;
  }
};

// 获取API基础URL（优先使用 axios 实例的 baseURL，取不到时按环境兜底）
import instance from './request';
export const getApiBaseUrl = () => {
  const byAxios = (instance?.defaults as any)?.baseURL;
  if (byAxios) return byAxios as string;
  const env = detectEnvironment();
  switch (env) {
    case 'docker':
      return '/api';
    case 'development':
      return 'http://localhost:3002/api';
    default:
      return '/api';
  }
};

// 构建完整的文件URL
export const buildFileUrl = (path: string): string => {
  if (!path) return '';
  
  // 如果已经是完整的HTTP URL，直接返回
  if (path.startsWith('http://') || path.startsWith('https://')) {
    return path;
  }
  
  const baseUrl = getBaseUrl();
  
  // 如果是相对路径（以/开头），拼接基础URL
  if (path.startsWith('/')) {
    return `${baseUrl}${path}`;
  }
  
  // 如果是云存储路径（不以/开头），添加https协议
  return `https://${path}`;
};

// 下载接口基础路径：强制 /api/test/v1，不影响其他接口
export const getDownloadBaseUrl = (): string => {
  const base = (instance?.defaults as any)?.baseURL || '';
  if (base) {
    try {
      const u = new URL(base, typeof window !== 'undefined' ? window.location.origin : 'http://localhost');
      // 若已包含 /api/test/v1 直接返回
      if (/\/api\/test\//.test(u.pathname)) return `${u.origin}${u.pathname}`;
      // 若以 /api 结尾，则补 /test/v1
      if (/\/api\/?$/.test(u.pathname)) return `${u.origin}${u.pathname.replace(/\/$/, '')}/test/v1`;
      // 兜底：固定为 /api/test/v1
      return `${u.origin}/api/test/v1`;
    } catch {}
  }
  return '/api/test/v1';
};

// 构建缩略图URL（兼容 fid 或完整路径）
export const buildThumbnailUrl = (thumbnail: string, filename?: string): string => {
  if (!thumbnail) return '';
  // 如果是可能的 fid（不含协议/斜杠），走下载接口
  if (!/^https?:\/\//i.test(thumbnail) && !thumbnail.startsWith('/')) {
    return buildDownloadUrlByFid(thumbnail, filename);
  }
  return buildFileUrl(thumbnail);
};

// 通过 fid 构建下载 URL
export const buildDownloadUrlByFid = (fid: string, filename?: string): string => {
  if (!fid) return '';
  const baseUrl = `${getDownloadBaseUrl()}/download?belveth_fid=${encodeURIComponent(fid)}`;
  if (filename) {
    return `${baseUrl}&name=${encodeURIComponent(filename)}`;
  }
  return baseUrl;
};

// 构建上传API URL
export const buildUploadUrl = (endpoint: string): string => {
  const apiBaseUrl = getApiBaseUrl();

  // 如果是相对路径的API端点
  if (endpoint.startsWith('/')) {
    return `${apiBaseUrl}${endpoint}`;
  }

  return `${apiBaseUrl}/${endpoint}`;
};

// 获取当前环境信息（用于调试）
export const getEnvironmentInfo = () => {
  const env = detectEnvironment();
  const baseUrl = getBaseUrl();
  const apiBaseUrl = getApiBaseUrl();
  
  return {
    environment: env,
    baseUrl,
    apiBaseUrl,
    location: {
      hostname: window.location.hostname,
      port: window.location.port,
      origin: window.location.origin
    }
  };
}; 