import instance from './request';
import { getDownloadBaseUrl } from './url';

// 全局缓存：同一 fid 只创建一次 ObjectURL，避免所有记录共享同一个 URL 的问题
const FID_OBJECT_URL_CACHE = new Map<string, string>();

export async function getObjectUrlByFid(fid: string): Promise<string> {
  if (!fid) return '';
  const cached = FID_OBJECT_URL_CACHE.get(fid);
  if (cached) return cached;

  try {
    const url = `${getDownloadBaseUrl()}/download?belveth_fid=${encodeURIComponent(fid)}`;
    const resp = await instance.request({ url, method: 'GET', responseType: 'blob' });
    // axios 返回 data 在拦截器已放行，这里直接是 Blob 或 { data: Blob }
    const blob: Blob = (resp instanceof Blob) ? resp : (resp?.data as Blob);

    if (!blob || blob.size === 0) {
      return '';
    }

    const objUrl = URL.createObjectURL(blob);
    FID_OBJECT_URL_CACHE.set(fid, objUrl);
    return objUrl;
  } catch (error) {
    console.error('Failed to get object URL for FID:', fid, error);
    return '';
  }
}

export function revokeObjectUrlForFid(fid: string) {
  const u = FID_OBJECT_URL_CACHE.get(fid);
  if (u) {
    URL.revokeObjectURL(u);
    FID_OBJECT_URL_CACHE.delete(fid);
  }
}

