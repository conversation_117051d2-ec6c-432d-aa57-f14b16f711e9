import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../store';
import { getCurrentAuth } from 'utils/request';

const namespace = 'user';
const TOKEN_NAME = 'tdesign-starter';

const USER_STATUS_STORAGE_KEY = '__TVI_USER_STATUS__';

const readStoredUserStatus = () => {
  try {
    const raw = localStorage.getItem(USER_STATUS_STORAGE_KEY);
    if (raw) return JSON.parse(raw);
  } catch {}
  return {};
};

const initialState = {
  token: localStorage.getItem(TOKEN_NAME) || 'main_token', // 默认token不走权限
  userInfo: readStoredUserStatus() || {},
};

// login
export const login = createAsyncThunk(`${namespace}/login`, async (userInfo: Record<string, unknown>) => {
  const mockLogin = async (userInfo: Record<string, unknown>) => {
    // 登录请求流程
    console.log(userInfo);
    // const { account, password } = userInfo;
    // if (account !== 'td') {
    //   return {
    //     code: 401,
    //     message: '账号不存在',
    //   };
    // }
    // if (['main_', 'dev_'].indexOf(password) === -1) {
    //   return {
    //     code: 401,
    //     message: '密码错误',
    //   };
    // }
    // const token = {
    //   main_: 'main_token',
    //   dev_: 'dev_token',
    // }[password];
    return {
      code: 200,
      message: '登陆成功',
      data: 'main_token',
    };
  };

  const res = await mockLogin(userInfo);
  if (res.code === 200) {
    return res.data;
  }
  throw res;
});

// getUserInfo
export const getUserInfo = createAsyncThunk(`${namespace}/getUserInfo`, async (_, { getState }: any) => {

  // 2) 等待 Authorization 准备就绪（最多 2 秒）
  const waitAuth = async (timeoutMs = 2000) => {
    const start = Date.now();
    while (!getCurrentAuth() && Date.now() - start < timeoutMs) {
      // 50ms 轮询等待 SDK 注入 Authorization
      // eslint-disable-next-line no-await-in-loop
      await new Promise((r) => setTimeout(r, 50));
    }
  };
  await waitAuth();
  const jwt = getCurrentAuth();

  // 调试日志
  // eslint-disable-next-line no-console
  console.log('[getUserInfo] jwt ready:', !!jwt);

  if (jwt) {
    try {
      const res = await fetch('https://belveth.v.qq.com/api/user/v1/status', {
        method: 'GET',
        credentials: 'include',
        headers: {
          Accept: 'application/json, text/plain, */*',
          Authorization: `Bearer ${jwt}`,
        },
      });
      // eslint-disable-next-line no-console
      console.log('[getUserInfo] status res.ok:', res.ok, 'status:', res.status);
      if (res.ok) {
        const data = await res.json();
        // 存储完整用户信息到 localStorage，便于后续读取 rtx / id
        try {
          localStorage.setItem(USER_STATUS_STORAGE_KEY, JSON.stringify(data));
        } catch {}
        // 兼容结构：name 为空时回退 id
        const name = data?.name || data?.id || 'TVI User';
        return { ...data, name, roles: ['all'] };
      }
    } catch (e) {
      // eslint-disable-next-line no-console
      console.warn('[getUserInfo] fetch status error:', e);
    }
  }

  // 3) 兼容旧逻辑：从本地 Redux token 推断（修复读取路径错误）
  const rootState = getState();
  const token = rootState?.user?.token;
  if (token === 'main_token') {
    return { name: 'td_main', roles: ['all'] };
  }
  // 默认回退
  return { name: 'TVI User', roles: ['userIndex', 'dashboardBase', 'login'] };
});

const userSlice = createSlice({
  name: namespace,
  initialState,
  reducers: {
    logout: (state) => {
      localStorage.removeItem(TOKEN_NAME);
      state.token = '';
      state.userInfo = {};
    },
    remove: (state) => {
      state.token = '';
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(login.fulfilled, (state, action) => {
        localStorage.setItem(TOKEN_NAME, action.payload);

        state.token = action.payload;
      })
      .addCase(getUserInfo.fulfilled, (state, action) => {
        state.userInfo = action.payload;
      });
  },
});

export const selectUser = (state: RootState) => state.user;

export const { logout, remove } = userSlice.actions;

export default userSlice.reducer;
