import React, { useEffect } from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import { HashRouter, useNavigate } from 'react-router-dom';
import store from 'modules/store';
import App from 'layouts/index';
import login from '@tencent/tvi-login';

import { getUserInfo } from 'modules/user';
import { getCurrentAuth } from 'utils/request';

import 'tdesign-react/es/style/index.css';

import './styles/index.less';

const env = import.meta.env.MODE || 'development';
const baseRouterName = env === 'site' ? '/starter/react/' : '';

// App 启动时初始化 TVI 登录
function TVILoginInitializer() {
  const navigate = useNavigate();
  useEffect(() => {
    (async () => {
      const devBypass = env === 'development' && typeof window !== 'undefined' && window.location?.port === '3001';
      if (devBypass) {
        // 开发环境（3001）下跳过 TVI 登录初始化与强制跳转，直接尝试拉取默认用户信息
        try {
          store.dispatch<any>(getUserInfo());
        } catch (e) {
          // 忽略
        }
        return;
      }

      try {
        await login.init({
          oauth: {
            role: 'center',
            session: /tvi.v.qq.com$/.test(window.location.host) ? 'cookie-shared' : 'isolated',
          },
        });
        // 若刷新后本地已存在 Authorization，初始化完成立即拉取用户信息
        if (getCurrentAuth()) {
          store.dispatch<any>(getUserInfo());
        }
        // 登录态变化：已登录则立即拉取用户信息
        login.addEventListener('authstatechange', async ({ authed }) => {
          try {
            if (authed) {
              store.dispatch<any>(getUserInfo());
            } else {
              navigate('/login', { replace: true });
            }
          } catch (e) {}
        });
      } catch (err) {
        // 初始化失败可视为未登录
        navigate('/login', { replace: true });
      }
    })();
  }, [navigate]);
  return null;
}

// eslint-disable-next-line @typescript-eslint/no-non-null-assertion
const root = document.getElementById('app')!;

const renderApp = () => {
  ReactDOM.createRoot(root).render(
    <Provider store={store}>
      <HashRouter>
        <TVILoginInitializer />
        <App />
      </HashRouter>
    </Provider>,
  );
};

renderApp();
