.panel {
  height: 100%;

  :global {
    .t-menu--scroll::-webkit-scrollbar {
      width: 8px;
      background: transparent;
    }
    .t-menu--scroll::-webkit-scrollbar-thumb {
      border-radius: 6px;
      border: 2px solid transparent;
      background-clip: content-box;
      background-color: #eeeeee;
    }

    .t-default-menu.t-menu--dark {
      background: var(--td-gray-color-13);
    }
    .t-default-menu:not(.t-menu--dark) .t-menu__item.t-is-active:not(.t-is-opened) {
      background-color: var(--td-brand-color-1);
      color: var(--td-brand-color);
      .t-icon {
        color: var(--td-brand-color);
      }
    }
  }
}
