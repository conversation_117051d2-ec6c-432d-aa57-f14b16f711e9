import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import login from '@tencent/tvi-login';

export default function RequireAuth({ children }: { children: React.ReactElement }) {
  const location = useLocation();
  const authed = !!(login as any)?.oauth?.authed;

  // 在本地开发环境（端口 3001）下跳过登录校验，便于开发调试
  const devBypass = import.meta.env.MODE === 'development' && typeof window !== 'undefined' && window.location?.port === '3001';
  if (devBypass) {
    return children;
  }

  if (!authed) {
    return <Navigate to="/login" replace state={{ from: location }} />;
  }
  return children;
}
