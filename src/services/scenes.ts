import { instance } from '../utils/request';

// 场景类型定义
export interface Scene {
  id: string;
  project_id?: string; // 响应中可能没有project_id
  name: string;
  description?: string; // 响应中可能没有description
  thumbnail_path?: string;
  thumbnail_fid?: string;
  asset_id?: string; // 向后兼容
  asset_list?: string[]; // 从字符串转换而来的数组
  scene_asset_id?: string;
  metadata?: string;
  status: number;
  created_at: string | number;
  updated_at: string | number;
  project_name?: string;
  asset_name?: string;
  source_file_url?: string;
  source_file_size?: number;
  created_by?: string;
  updated_by?: string;
}

// 创建场景请求类型
export interface SceneCreateData {
  project_id: string;
  name: string;
  desc?: string;
  asset_list?: string[];
  scene_asset_id?: string;
  thumbnail_fid?: string;
}

// 更新场景请求类型
export interface SceneUpdateData {
  project_id?: string;
  name?: string;
  desc?: string;
  asset_list?: string[];
  scene_asset_id?: string;
  thumbnail_fid?: string;
  status?: number;
}

// 场景列表查询参数
export interface SceneListParams {
  page?: number;
  page_size?: number;
  search?: string;
  project_id?: string;
  status?: number;
}

// 场景列表响应类型
export interface SceneListResponse {
  scene: Scene[];
  pagination?: {
    total: number;
    page: number;
    page_size: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// 场景服务类
export class SceneService {
  /**
   * 获取场景列表
   */
  static async getList(params: SceneListParams = {}): Promise<SceneListResponse> {
    const { page = 1, page_size = 10, search, project_id, status } = params;

    // 构造后端要求结构：POST /core/search_scene_list
    const body: any = {
      pagination: {
        page: Number(page),
        page_size: Number(page_size),
      },
      sort: 1 as number,
    };

    // 添加筛选条件
    const filters: any[] = [];
    if (search && search.trim()) {
      filters.push({
        field: 'name',
        operation: 0,
        value: search.trim(),
      });
    }
    if (project_id) {
      filters.push({
        field: 'project_id',
        operation: 0,
        value: project_id,
      });
    }
    if (status !== undefined) {
      filters.push({
        field: 'status',
        operation: 0,
        value: status,
      });
    }
    if (filters.length > 0) {
      body.filters = filters;
    }

    // 确保数字类型
    if (typeof body.sort !== 'number') body.sort = Number(body.sort) || 1;
    if (typeof body.pagination?.page !== 'number') body.pagination.page = Number(body.pagination?.page) || 1;
    if (typeof body.pagination?.page_size !== 'number')
      body.pagination.page_size = Number(body.pagination?.page_size) || Number(page_size) || 10;

    // 使用 POST /core/search_scene_list
    const respData: any = await instance.request({
      url: '/core/search_scene_list',
      method: 'POST',
      data: body,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

    // 根据实际响应结构解析：{ scenes: [...], page_info: {...} }
    const listRaw = respData?.scenes || [];
    const pageInfo = respData?.page_info || {};

    const mappedList: Scene[] = listRaw.map((s: any) => ({
      id: s.id || s.scene_id,
      project_id: s.project_id || '', // 响应中可能没有project_id
      name: s.name || '',
      description: s.desc || s.description || '', // 响应中可能没有description
      status: s.status ?? 1,
      created_at: s.created_at || '',
      updated_at: s.updated_at || '',
      // 同时保留 thumbnail_fid 并映射到 thumbnail_path 以供渲染
      thumbnail_fid: s.thumbnail_fid || '',
      thumbnail_path: s.thumbnail_fid || s.thumbnail_path || '',
      // asset_list 是字符串，需要转换为数组
      asset_list: s.asset_list ? s.asset_list.split(',').filter(Boolean) : [],
      scene_asset_id: s.scene_asset_id || '',
      metadata: s.metadata || '',
      project_name: s.project_name || '',
      asset_name: s.asset_name || '',
      created_by: s.created_by || '',
      updated_by: s.updated_by || '',
    }));

    // 使用实际的分页信息
    const total = pageInfo.total || mappedList.length;
    const currentPage = pageInfo.page || page;
    const currentPageSize = pageInfo.page_size || page_size;

    return {
      scene: mappedList,
      pagination: {
        total,
        page: currentPage,
        page_size: currentPageSize,
        totalPages: Math.ceil(total / currentPageSize) || 0,
        hasNext: total > currentPage * currentPageSize,
        hasPrev: currentPage > 1,
      },
    };
  }

  /**
   * 获取场景详情
   */
  static async getById(sceneId: string, projectId: string): Promise<Scene> {
    // 新接口：GET /core/get_scene?scene_id=xxx&project_id=xxx
    const respData: any = await instance.get('/core/get_scene', {
      params: {
        scene_id: sceneId,
        project_id: projectId
      }
    });

    // 根据实际响应格式解析：{ scene: {...}, rsp: null }
    const sceneData = respData?.scene;
    if (!sceneData) {
      throw new Error('场景数据不存在');
    }

    // 映射响应数据到Scene接口
    return {
      id: sceneData.id,
      project_id: projectId, // 从参数获取
      name: sceneData.name || '',
      description: sceneData.desc || sceneData.description || '',
      status: sceneData.status ?? 1,
      created_at: sceneData.created_at || '',
      updated_at: sceneData.updated_at || '',
      thumbnail_fid: sceneData.thumbnail_fid || '',
      thumbnail_path: sceneData.thumbnail_fid || sceneData.thumbnail_path || '',
      // asset_list 是字符串，需要转换为数组
      asset_list: sceneData.asset_list ? sceneData.asset_list.split(',').filter(Boolean) : [],
      scene_asset_id: sceneData.scene_asset_id || '',
      metadata: sceneData.metadata || '',
      project_name: sceneData.project_name || '',
      asset_name: sceneData.asset_name || '',
      created_by: sceneData.created_by || '',
      updated_by: sceneData.updated_by || '',
    };
  }

  /**
   * 创建场景
   */
  static async create(data: SceneCreateData): Promise<{ id: string }> {
    const respData: any = await instance.post('/core/create_scene', data);

    // 根据实际响应格式解析：{ scene_id: "...", rsp: {...} }
    const sceneId = respData?.scene_id;
    if (!sceneId) {
      throw new Error('创建场景失败，未返回场景ID');
    }

    return { id: sceneId };
  }

  /**
   * 更新场景
   */
  static async update(sceneId: string, data: SceneUpdateData): Promise<void> {
    // 新的更新接口：POST /core/update_scene，需携带 scene_id
    const respData: any = await instance.post('/core/update_scene', { scene_id: sceneId, ...data });

    // 检查响应状态
    if (respData?.rsp?.code !== 0) {
      throw new Error(respData?.rsp?.msg || '更新场景失败');
    }
  }

  /**
   * 删除场景
   */
  static async delete(sceneId: string, projectId: string): Promise<void> {
    const respData: any = await instance.post('/core/delete_scene', {
      scene_id: sceneId,
      project_id: projectId
    });

    // 根据实际响应格式检查：{ rsp: { code: 0, msg: "success" } }
    if (respData?.rsp?.code !== 0) {
      throw new Error(respData?.rsp?.msg || '删除场景失败');
    }
  }

  /**
   * 获取项目下的场景列表（使用新接口）
   */
  static async getByProject(projectId: string, params: {
    page?: number;
    page_size?: number;
    status?: number;
  } = {}) {
    return this.getList({
      project_id: projectId,
      ...params
    });
  }

  /**
   * 批量删除场景
   */
  static async batchDelete(ids: string[], projectId: string): Promise<any> {
    // 逐个删除，因为新接口需要project_id
    const deletePromises = ids.map(id => this.delete(id, projectId));
    return Promise.all(deletePromises);
  }

  // 向后兼容的方法
  /**
   * @deprecated 使用 getList 替代
   */
  static async list(params: {
    project_id?: string;
    offset?: number;
    limit?: number;
    status?: number;
    name?: string;
  } = {}) {
    const { offset = 0, limit = 10, name, ...otherParams } = params;
    const page = Math.floor(offset / limit) + 1;
    const response = await this.getList({
      page,
      page_size: limit,
      search: name,
      ...otherParams
    });
    return {
      data: response.scene || [],
      total: response.pagination?.total || 0,
    };
  }
}

export default SceneService;
