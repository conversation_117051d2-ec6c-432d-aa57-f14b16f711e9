import { instance } from '../utils/request';
import { NotificationService } from './notification';

// 标记上传任务失败
export async function markUploadTaskFailed(
  assetId: string,
  jobId?: string,
  totalChunks?: number,
  failReason: string = 'User retry - marking previous task as failed'
) {
  if (!jobId || !totalChunks || totalChunks <= 0) {
    console.warn('[markUploadTaskFailed] 缺少必要参数，跳过结束任务调用');
    return;
  }

  try {
    const requestBody = {
      asset_id: assetId,
      fail_reason: failReason,
      job_id: jobId,
      success: false,
      total: totalChunks,
    };

    console.log('[markUploadTaskFailed] 发送失败通知:', requestBody);

    await instance.post('/asset_finish_upload', requestBody);

    console.log('[markUploadTaskFailed] 失败通知发送成功');
  } catch (error) {
    console.warn('[markUploadTaskFailed] 发送失败通知失败:', error);
    // 不抛出错误，因为这只是清理操作
  }
}

const DEFAULT_CHUNK_SIZE = 5 * 1024 * 1024; // 5MB

export async function uploadAssetSourceInBackground(params: {
  file: File;
  assetId: string;
  assetName: string;
  onProgress?: (progress: number, transferredSize: number, speed?: number) => void;
  onError?: (error: string) => void;
  onComplete?: () => void;
  onCancel?: () => void; // 用户取消上传时的回调
  abortController?: AbortController;
  taskId?: string; // 用于保存断点续传数据
  resumeFromChunk?: number; // 从哪个分片开始恢复
  onJobIdReceived?: (jobId: string) => void; // 获取到jobId时的回调
}) {
  const { file, assetId, assetName, onProgress, onError, onComplete, onCancel, abortController, taskId, resumeFromChunk, onJobIdReceived } = params;

  // 在函数作用域定义变量，以便在catch块中使用
  let jobId: string = '';
  let totalChunks: number = 0;
  let fileHash: string = '';

  // 计算文件哈希值（用于断点续传验证）
  const calculateFileHash = async (file: File): Promise<string> => {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const hashBuffer = await crypto.subtle.digest('SHA-256', arrayBuffer);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    } catch (error) {
      console.warn('计算文件哈希失败:', error);
      return '';
    }
  };

  try {
    // 计算分片总数
    totalChunks = Math.ceil(file.size / DEFAULT_CHUNK_SIZE);

    if (resumeFromChunk && taskId) {
      // 断点续传模式：从保存的数据中恢复jobId
      console.log('[uploadAssetSourceInBackground] 断点续传模式，恢复任务信息...');

      try {
        const resumeDataStr = localStorage.getItem(`upload_resume_${taskId}`);
        if (resumeDataStr) {
          const resumeData = JSON.parse(resumeDataStr);
          jobId = resumeData.jobId;
          fileHash = resumeData.fileHash || '';

          if (!jobId) {
            throw new Error('断点续传数据中缺少jobId');
          }

          console.log('[uploadAssetSourceInBackground] 断点续传任务恢复成功，jobId:', jobId);
        } else {
          throw new Error('未找到断点续传数据');
        }
      } catch (error) {
        console.error('[uploadAssetSourceInBackground] 恢复断点续传数据失败:', error);
        throw new Error('恢复断点续传数据失败，请重新上传');
      }

      // 通知jobId已获取
      onJobIdReceived?.(jobId);

      // 通知器：任务恢复
      NotificationService.startTask(jobId, file.name);
    } else {
      // 新上传模式：创建新任务
      console.log('[uploadAssetSourceInBackground] 新上传模式，创建新任务...');

      // 计算文件哈希
      if (taskId) {
        console.log('[uploadAssetSourceInBackground] 计算文件哈希值...');
        fileHash = await calculateFileHash(file);
        console.log('[uploadAssetSourceInBackground] 文件哈希计算完成');
      }

      // 启动上传任务
      const startResp: any = await instance.post('/asset_start_upload', {
        asset_id: assetId,
        file_name: file.name, // 使用完整文件名（包含扩展名）
        content_type: file.type || 'application/octet-stream',
      });
      jobId = startResp?.upload_job?.id;
      if (!jobId) throw new Error('未获取到上传任务ID');

      // 通知jobId已获取
      onJobIdReceived?.(jobId);

      // 通知器：任务开始
      NotificationService.startTask(jobId, file.name);
    }

    // 3) 使用 ChunkedUploader 的分片上传逻辑
    const uploaded = new Set<number>();
    const startTime = Date.now();

    // 如果是断点续传，从指定分片开始
    const startChunk = resumeFromChunk || 1;
    if (resumeFromChunk && resumeFromChunk > 1) {
      // 标记之前的分片为已上传
      for (let i = 1; i < resumeFromChunk; i++) {
        uploaded.add(i);
      }
    }

    // 保存断点续传数据到localStorage
    const saveUploadProgress = (currentChunk: number) => {
      if (taskId) {
        try {
          const resumeData = {
            assetId,
            assetName,
            fileName: file.name,
            fileSize: file.size,
            totalChunks,
            currentChunk,
            jobId,
            fileHash, // 添加文件哈希值
            timestamp: Date.now()
          };
          localStorage.setItem(`upload_resume_${taskId}`, JSON.stringify(resumeData));
        } catch (error) {
          console.warn('Failed to save upload progress:', error);
        }
      }
    };

    // 依序上传每个分片
    for (let i = startChunk; i <= totalChunks; i++) {
      // 检查是否被取消
      if (abortController?.signal.aborted) {
        console.log('[uploadAssetSourceInBackground] 上传已被取消');
        onCancel?.(); // 调用取消回调
        return; // 直接返回，不抛出错误，保留断点续传数据
      }

      const start = (i - 1) * DEFAULT_CHUNK_SIZE;
      const end = Math.min(start + DEFAULT_CHUNK_SIZE, file.size);
      const chunk = file.slice(start, end);

      // 上传分片 - 参考 ChunkedUploader 的 uploadPart 逻辑
      let fileApiBase = '/api';
      try {
        const base = (instance.defaults as any)?.baseURL || '';
        const u = new URL(base, typeof window !== 'undefined' ? window.location.origin : '');
        fileApiBase = `${u.origin}/api`;
      } catch {}

      const url = `${fileApiBase}/file/v3/1025/uploadPart?num=${i}&ticket=${jobId}&v=4`;

      try {
        await instance.request({
          url,
          method: 'PUT',
          data: chunk,
          headers: { 'Content-Type': 'application/octet-stream' },
          signal: abortController?.signal,
        });
      } catch (error: any) {
        // 如果是用户主动取消，直接返回，保留断点续传数据
        if (error.name === 'AbortError' || abortController?.signal.aborted) {
          console.log('[uploadAssetSourceInBackground] 分片上传被用户取消');
          onCancel?.(); // 调用取消回调
          return;
        }
        // 其他错误继续抛出
        throw error;
      }

      uploaded.add(i);
      const progress = Math.round((uploaded.size / totalChunks) * 100);
      const transferredSize = uploaded.size * DEFAULT_CHUNK_SIZE;
      const elapsedTime = (Date.now() - startTime) / 1000;
      const speed = elapsedTime > 0 ? transferredSize / elapsedTime : 0;

      // 保存断点续传进度
      saveUploadProgress(i);

      // 更新传输管理器进度
      onProgress?.(progress, Math.min(transferredSize, file.size), speed);

      // 保持原有的通知服务
      NotificationService.updateProgress(jobId, progress);
    }

    // 4) 完成：通知后端 asset_finish_upload（带重试机制）
    const finishBody = {
      asset_id: assetId,
      fail_reason: '',
      job_id: jobId,
      success: true,
      total: totalChunks,
    };

    const MAX_FINISH_RETRIES = 5;
    let finishedOk = false;
    let lastErr: any = null;

    for (let attempt = 1; attempt <= MAX_FINISH_RETRIES && !finishedOk; attempt += 1) {
      try {
        await instance.post('/asset_finish_upload', finishBody);
        finishedOk = true;
      } catch (e: any) {
        lastErr = e;
        const code = e?.code ?? e?.rsp?.code;
        const msg: string = e?.msg || e?.message || '';
        // 更宽松的重试条件：明确提示可重试、5xx、无code（如网络错误）、或者兜底在最大次数内都重试
        const needRetry = code === 10000 || /retry\s*:\s*true/i.test(msg) || (e?.status && e.status >= 500) || code === undefined || true;

        if (attempt < MAX_FINISH_RETRIES && needRetry) {
          // 线性退避：300ms, 600ms, 900ms, 1200ms
          await new Promise(r => setTimeout(r, 300 * attempt));
        }
      }
    }

    if (!finishedOk) {
      throw new Error(`完成上传失败（重试${MAX_FINISH_RETRIES}次）: ${lastErr?.message || '未知错误'}`);
    }

    // 清理断点续传数据
    if (taskId) {
      try {
        localStorage.removeItem(`upload_resume_${taskId}`);
      } catch (error) {
        console.warn('Failed to clear upload progress:', error);
      }
    }

    NotificationService.completeTask(jobId);
    onComplete?.();
  } catch (error: any) {
    if ((error as any)?.name === 'AbortError' || abortController?.signal.aborted) {
      // 用户主动取消，调用asset_finish_upload标记任务失败
      console.log('[uploadAssetSourceInBackground] 用户主动取消，标记任务失败');
      await markUploadTaskFailed(assetId, jobId, totalChunks, '用户取消上传');
      onCancel?.(); // 调用取消回调
      return;
    }

    const errorMessage = error?.message || '上传失败';
    NotificationService.failTask(assetId, errorMessage);
    onError?.(errorMessage);
    throw error;
  }
}

