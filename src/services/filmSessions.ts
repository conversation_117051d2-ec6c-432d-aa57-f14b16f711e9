import { instance } from '../utils/request';

// 场次类型定义
export interface FilmSession {
  id: string;
  scene_id: string;
  name: string;
  data: string;
  version: number;
  thumbnail_fid: string;
  description?: string;
  project_id?: string; // 预留给后端，用于显示所属项目
}

// 场次搜索请求参数类型
export interface FilmSessionSearchParams {
  page?: number;
  page_size?: number;
  search?: string;
  scene_id?: string;
  project_id?: string;
}

// 场次搜索响应类型
export interface FilmSessionSearchResponse {
  film_sesion_list: FilmSession[];
  rsp: {
    code: number;
    msg: string;
  };
}

// 场次更新请求类型
export interface FilmSessionUpdateData {
  data: string;
  project_id: string;
  session_id: string;
  version: number;
  thumbnail_fid?: string;
  desc?: string;
}



// 场次详情响应类型
export interface FilmSessionDetailResponse {
  session: FilmSession;
}

// 场次服务类
export class FilmSessionService {
  /**
   * 搜索场次列表
   */
  static async searchList(params: FilmSessionSearchParams = {}): Promise<{
    data: FilmSession[];
    total: number;
  }> {
    const { page = 1, page_size = 10, search, scene_id, project_id } = params;

    // 构造后端要求结构：POST /core/search_film_session_list
    const body: any = {
      pagination: {
        page: Number(page),
        page_size: Number(page_size),
      },
      sort: 1 as number,
    };

    // 添加筛选条件
    const filters: any[] = [];
    if (search && search.trim()) {
      filters.push({
        field: 'name',
        operation: 0,
        value: search.trim(),
      });
    }
    if (scene_id) {
      filters.push({
        field: 'scene_id',
        operation: 0,
        value: scene_id,
      });
    }
    if (project_id) {
      filters.push({
        field: 'project_id',
        operation: 0,
        value: project_id,
      });
    }
    if (filters.length > 0) {
      body.filters = filters;
    }

    // 确保数字类型
    if (typeof body.sort !== 'number') body.sort = Number(body.sort) || 1;
    if (typeof body.pagination?.page !== 'number') body.pagination.page = Number(body.pagination?.page) || 1;
    if (typeof body.pagination?.page_size !== 'number')
      body.pagination.page_size = Number(body.pagination?.page_size) || Number(page_size) || 10;

    // 使用 POST /core/search_film_session_list
    const respData: FilmSessionSearchResponse = await instance.request({
      url: '/core/search_film_session_list',
      method: 'POST',
      data: body,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

    // 检查响应状态
    if (respData?.rsp?.code !== 0) {
      throw new Error(respData?.rsp?.msg || '获取场次列表失败');
    }

    // 解析响应数据 - 注意API中的字段名拼写
    const listRaw = respData?.film_sesion_list || (respData as any)?.film_session_list || [];
    
    // 数据转换和清理
    const filmSessions: FilmSession[] = listRaw.map((item: any) => ({
      id: item.id || '',
      scene_id: item.scene_id || '',
      name: item.name || '',
      data: item.data || '',
      version: Number(item.version) || 0,
      thumbnail_fid: item.thumbnail_fid || '',
      description: item.description || '',
      project_id: item.project_id || '', // 预留字段，后端会补充
    }));

    return {
      data: filmSessions,
      total: filmSessions.length, // 注意：新API可能没有返回总数，这里使用当前页数据长度
    };
  }



  /**
   * 更新场次信息
   * 注意：save_film_session 接口实际上是更新现有场次的信息，不是创建新场次
   */
  static async update(data: FilmSessionUpdateData): Promise<void> {
    // 使用 POST /core/save_film_session 更新场次信息
    const respData: any = await instance.post('/core/save_film_session', data);

    // 检查响应状态 - 注意新API返回的是 { rsp: null }
    if (respData?.rsp !== null && respData?.rsp?.code !== 0) {
      throw new Error(respData?.rsp?.msg || '更新场次失败');
    }
  }

  /**
   * 获取场次详情
   */
  static async getById(sessionId: string, projectId: string): Promise<FilmSession> {
    // 使用 GET /core/get_film_session?session_id=xxx&project_id=xxx
    const respData: FilmSessionDetailResponse = await instance.get('/core/get_film_session', {
      params: {
        session_id: sessionId,
        project_id: projectId
      }
    });

    // 解析响应数据
    const sessionData = respData?.session;
    if (!sessionData) {
      throw new Error('场次数据不存在');
    }

    // 数据转换和清理
    return {
      id: sessionData.id || '',
      scene_id: sessionData.scene_id || '',
      name: sessionData.name || '',
      data: sessionData.data || '',
      version: Number(sessionData.version) || 0,
      thumbnail_fid: sessionData.thumbnail_fid || '',
      description: sessionData.description || '',
      project_id: sessionData.project_id || '', // 预留字段，后端会补充
    };
  }


}

export default FilmSessionService;
