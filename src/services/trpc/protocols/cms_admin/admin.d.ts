import * as $protobuf from "protobufjs";
import Long = require("long");
/** Namespace trpc. */
export namespace trpc {

    /** Namespace video_media. */
    namespace video_media {

        /** Namespace cms_admin. */
        namespace cms_admin {

            /** AssetType enum. */
            enum AssetType {
                ASSET_TYPE_UNSPECIFIED = 0,
                MAP = 1,
                CHARACTER = 2,
                SOUND = 3
            }

            /** Properties of an Asset. */
            interface IAsset {

                /** Asset id */
                id?: (string|null);

                /** Asset tags */
                tags?: (string[]|null);

                /** Asset asset_type */
                asset_type?: (trpc.video_media.cms_admin.AssetType|null);

                /** Asset data */
                data?: (string|null);

                /** Asset state */
                state?: (number|null);

                /** Asset project_id_list */
                project_id_list?: (string[]|null);

                /** Asset created_at */
                created_at?: (number|Long|null);

                /** Asset created_by */
                created_by?: (string|null);
            }

            /** Represents an Asset. */
            class Asset implements IAsset {

                /**
                 * Constructs a new Asset.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.cms_admin.IAsset);

                /** Asset id. */
                public id: string;

                /** Asset tags. */
                public tags: string[];

                /** Asset asset_type. */
                public asset_type: trpc.video_media.cms_admin.AssetType;

                /** Asset data. */
                public data: string;

                /** Asset state. */
                public state: number;

                /** Asset project_id_list. */
                public project_id_list: string[];

                /** Asset created_at. */
                public created_at: (number|Long);

                /** Asset created_by. */
                public created_by: string;

                /**
                 * Encodes the specified Asset message. Does not implicitly {@link trpc.video_media.cms_admin.Asset.verify|verify} messages.
                 * @param message Asset message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.cms_admin.IAsset, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes an Asset message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns Asset
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.cms_admin.Asset;

                /**
                 * Verifies an Asset message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates an Asset message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns Asset
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.cms_admin.Asset;

                /**
                 * Creates a plain object from an Asset message. Also converts values to other types if specified.
                 * @param message Asset
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.cms_admin.Asset, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this Asset to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for Asset
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a StartUploadRequest. */
            interface IStartUploadRequest {

                /** StartUploadRequest asset_id */
                asset_id?: (string|null);

                /** StartUploadRequest file_name */
                file_name?: (string|null);

                /** StartUploadRequest content_type */
                content_type?: (string|null);
            }

            /** Represents a StartUploadRequest. */
            class StartUploadRequest implements IStartUploadRequest {

                /**
                 * Constructs a new StartUploadRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.cms_admin.IStartUploadRequest);

                /** StartUploadRequest asset_id. */
                public asset_id: string;

                /** StartUploadRequest file_name. */
                public file_name: string;

                /** StartUploadRequest content_type. */
                public content_type: string;

                /**
                 * Encodes the specified StartUploadRequest message. Does not implicitly {@link trpc.video_media.cms_admin.StartUploadRequest.verify|verify} messages.
                 * @param message StartUploadRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.cms_admin.IStartUploadRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a StartUploadRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns StartUploadRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.cms_admin.StartUploadRequest;

                /**
                 * Verifies a StartUploadRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a StartUploadRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns StartUploadRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.cms_admin.StartUploadRequest;

                /**
                 * Creates a plain object from a StartUploadRequest message. Also converts values to other types if specified.
                 * @param message StartUploadRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.cms_admin.StartUploadRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this StartUploadRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for StartUploadRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a StartUploadResponse. */
            interface IStartUploadResponse {

                /** StartUploadResponse upload_job */
                upload_job?: (trpc.video_media.cms_admin.IAssetUploadJob|null);
            }

            /** Represents a StartUploadResponse. */
            class StartUploadResponse implements IStartUploadResponse {

                /**
                 * Constructs a new StartUploadResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.cms_admin.IStartUploadResponse);

                /** StartUploadResponse upload_job. */
                public upload_job?: (trpc.video_media.cms_admin.IAssetUploadJob|null);

                /**
                 * Encodes the specified StartUploadResponse message. Does not implicitly {@link trpc.video_media.cms_admin.StartUploadResponse.verify|verify} messages.
                 * @param message StartUploadResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.cms_admin.IStartUploadResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a StartUploadResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns StartUploadResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.cms_admin.StartUploadResponse;

                /**
                 * Verifies a StartUploadResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a StartUploadResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns StartUploadResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.cms_admin.StartUploadResponse;

                /**
                 * Creates a plain object from a StartUploadResponse message. Also converts values to other types if specified.
                 * @param message StartUploadResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.cms_admin.StartUploadResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this StartUploadResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for StartUploadResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of an AssetUploadJob. */
            interface IAssetUploadJob {

                /** AssetUploadJob id */
                id?: (string|null);
            }

            /** Represents an AssetUploadJob. */
            class AssetUploadJob implements IAssetUploadJob {

                /**
                 * Constructs a new AssetUploadJob.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.cms_admin.IAssetUploadJob);

                /** AssetUploadJob id. */
                public id: string;

                /**
                 * Encodes the specified AssetUploadJob message. Does not implicitly {@link trpc.video_media.cms_admin.AssetUploadJob.verify|verify} messages.
                 * @param message AssetUploadJob message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.cms_admin.IAssetUploadJob, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes an AssetUploadJob message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns AssetUploadJob
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.cms_admin.AssetUploadJob;

                /**
                 * Verifies an AssetUploadJob message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates an AssetUploadJob message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns AssetUploadJob
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.cms_admin.AssetUploadJob;

                /**
                 * Creates a plain object from an AssetUploadJob message. Also converts values to other types if specified.
                 * @param message AssetUploadJob
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.cms_admin.AssetUploadJob, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this AssetUploadJob to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for AssetUploadJob
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a FinishUploadRequest. */
            interface IFinishUploadRequest {

                /** FinishUploadRequest job_id */
                job_id?: (string|null);

                /** FinishUploadRequest asset_id */
                asset_id?: (string|null);

                /** FinishUploadRequest success */
                success?: (boolean|null);

                /** FinishUploadRequest fail_reason */
                fail_reason?: (string|null);
            }

            /** Represents a FinishUploadRequest. */
            class FinishUploadRequest implements IFinishUploadRequest {

                /**
                 * Constructs a new FinishUploadRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.cms_admin.IFinishUploadRequest);

                /** FinishUploadRequest job_id. */
                public job_id: string;

                /** FinishUploadRequest asset_id. */
                public asset_id: string;

                /** FinishUploadRequest success. */
                public success: boolean;

                /** FinishUploadRequest fail_reason. */
                public fail_reason: string;

                /**
                 * Encodes the specified FinishUploadRequest message. Does not implicitly {@link trpc.video_media.cms_admin.FinishUploadRequest.verify|verify} messages.
                 * @param message FinishUploadRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.cms_admin.IFinishUploadRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a FinishUploadRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns FinishUploadRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.cms_admin.FinishUploadRequest;

                /**
                 * Verifies a FinishUploadRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a FinishUploadRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns FinishUploadRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.cms_admin.FinishUploadRequest;

                /**
                 * Creates a plain object from a FinishUploadRequest message. Also converts values to other types if specified.
                 * @param message FinishUploadRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.cms_admin.FinishUploadRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this FinishUploadRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for FinishUploadRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a FinishUploadResponse. */
            interface IFinishUploadResponse {

                /** FinishUploadResponse success */
                success?: (boolean|null);
            }

            /** Represents a FinishUploadResponse. */
            class FinishUploadResponse implements IFinishUploadResponse {

                /**
                 * Constructs a new FinishUploadResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.cms_admin.IFinishUploadResponse);

                /** FinishUploadResponse success. */
                public success: boolean;

                /**
                 * Encodes the specified FinishUploadResponse message. Does not implicitly {@link trpc.video_media.cms_admin.FinishUploadResponse.verify|verify} messages.
                 * @param message FinishUploadResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.cms_admin.IFinishUploadResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a FinishUploadResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns FinishUploadResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.cms_admin.FinishUploadResponse;

                /**
                 * Verifies a FinishUploadResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a FinishUploadResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns FinishUploadResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.cms_admin.FinishUploadResponse;

                /**
                 * Creates a plain object from a FinishUploadResponse message. Also converts values to other types if specified.
                 * @param message FinishUploadResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.cms_admin.FinishUploadResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this FinishUploadResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for FinishUploadResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a CreateAssetRequest. */
            interface ICreateAssetRequest {

                /** CreateAssetRequest tags */
                tags?: (string[]|null);

                /** CreateAssetRequest asset_type */
                asset_type?: (trpc.video_media.cms_admin.AssetType|null);

                /** CreateAssetRequest data */
                data?: (string|null);

                /** CreateAssetRequest project_id_list */
                project_id_list?: (string[]|null);
            }

            /** Represents a CreateAssetRequest. */
            class CreateAssetRequest implements ICreateAssetRequest {

                /**
                 * Constructs a new CreateAssetRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.cms_admin.ICreateAssetRequest);

                /** CreateAssetRequest tags. */
                public tags: string[];

                /** CreateAssetRequest asset_type. */
                public asset_type: trpc.video_media.cms_admin.AssetType;

                /** CreateAssetRequest data. */
                public data: string;

                /** CreateAssetRequest project_id_list. */
                public project_id_list: string[];

                /**
                 * Encodes the specified CreateAssetRequest message. Does not implicitly {@link trpc.video_media.cms_admin.CreateAssetRequest.verify|verify} messages.
                 * @param message CreateAssetRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.cms_admin.ICreateAssetRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a CreateAssetRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns CreateAssetRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.cms_admin.CreateAssetRequest;

                /**
                 * Verifies a CreateAssetRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a CreateAssetRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns CreateAssetRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.cms_admin.CreateAssetRequest;

                /**
                 * Creates a plain object from a CreateAssetRequest message. Also converts values to other types if specified.
                 * @param message CreateAssetRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.cms_admin.CreateAssetRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this CreateAssetRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for CreateAssetRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a CreateAssetResponse. */
            interface ICreateAssetResponse {

                /** CreateAssetResponse asset_id */
                asset_id?: (string|null);
            }

            /** Represents a CreateAssetResponse. */
            class CreateAssetResponse implements ICreateAssetResponse {

                /**
                 * Constructs a new CreateAssetResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.cms_admin.ICreateAssetResponse);

                /** CreateAssetResponse asset_id. */
                public asset_id: string;

                /**
                 * Encodes the specified CreateAssetResponse message. Does not implicitly {@link trpc.video_media.cms_admin.CreateAssetResponse.verify|verify} messages.
                 * @param message CreateAssetResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.cms_admin.ICreateAssetResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a CreateAssetResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns CreateAssetResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.cms_admin.CreateAssetResponse;

                /**
                 * Verifies a CreateAssetResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a CreateAssetResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns CreateAssetResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.cms_admin.CreateAssetResponse;

                /**
                 * Creates a plain object from a CreateAssetResponse message. Also converts values to other types if specified.
                 * @param message CreateAssetResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.cms_admin.CreateAssetResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this CreateAssetResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for CreateAssetResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a GetAssetRequest. */
            interface IGetAssetRequest {

                /** GetAssetRequest asset_id */
                asset_id?: (string|null);
            }

            /** Represents a GetAssetRequest. */
            class GetAssetRequest implements IGetAssetRequest {

                /**
                 * Constructs a new GetAssetRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.cms_admin.IGetAssetRequest);

                /** GetAssetRequest asset_id. */
                public asset_id: string;

                /**
                 * Encodes the specified GetAssetRequest message. Does not implicitly {@link trpc.video_media.cms_admin.GetAssetRequest.verify|verify} messages.
                 * @param message GetAssetRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.cms_admin.IGetAssetRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a GetAssetRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns GetAssetRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.cms_admin.GetAssetRequest;

                /**
                 * Verifies a GetAssetRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a GetAssetRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns GetAssetRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.cms_admin.GetAssetRequest;

                /**
                 * Creates a plain object from a GetAssetRequest message. Also converts values to other types if specified.
                 * @param message GetAssetRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.cms_admin.GetAssetRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this GetAssetRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for GetAssetRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a GetAssetResponse. */
            interface IGetAssetResponse {

                /** GetAssetResponse asset */
                asset?: (trpc.video_media.cms_admin.IAsset|null);
            }

            /** Represents a GetAssetResponse. */
            class GetAssetResponse implements IGetAssetResponse {

                /**
                 * Constructs a new GetAssetResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.cms_admin.IGetAssetResponse);

                /** GetAssetResponse asset. */
                public asset?: (trpc.video_media.cms_admin.IAsset|null);

                /**
                 * Encodes the specified GetAssetResponse message. Does not implicitly {@link trpc.video_media.cms_admin.GetAssetResponse.verify|verify} messages.
                 * @param message GetAssetResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.cms_admin.IGetAssetResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a GetAssetResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns GetAssetResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.cms_admin.GetAssetResponse;

                /**
                 * Verifies a GetAssetResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a GetAssetResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns GetAssetResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.cms_admin.GetAssetResponse;

                /**
                 * Creates a plain object from a GetAssetResponse message. Also converts values to other types if specified.
                 * @param message GetAssetResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.cms_admin.GetAssetResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this GetAssetResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for GetAssetResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of an UpdateAssetRequest. */
            interface IUpdateAssetRequest {

                /** UpdateAssetRequest asset_id */
                asset_id?: (string|null);

                /** UpdateAssetRequest tags */
                tags?: (string[]|null);

                /** UpdateAssetRequest data */
                data?: (string|null);

                /** UpdateAssetRequest project_id_list */
                project_id_list?: (string[]|null);
            }

            /** Represents an UpdateAssetRequest. */
            class UpdateAssetRequest implements IUpdateAssetRequest {

                /**
                 * Constructs a new UpdateAssetRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.cms_admin.IUpdateAssetRequest);

                /** UpdateAssetRequest asset_id. */
                public asset_id: string;

                /** UpdateAssetRequest tags. */
                public tags: string[];

                /** UpdateAssetRequest data. */
                public data: string;

                /** UpdateAssetRequest project_id_list. */
                public project_id_list: string[];

                /**
                 * Encodes the specified UpdateAssetRequest message. Does not implicitly {@link trpc.video_media.cms_admin.UpdateAssetRequest.verify|verify} messages.
                 * @param message UpdateAssetRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.cms_admin.IUpdateAssetRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes an UpdateAssetRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns UpdateAssetRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.cms_admin.UpdateAssetRequest;

                /**
                 * Verifies an UpdateAssetRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates an UpdateAssetRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns UpdateAssetRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.cms_admin.UpdateAssetRequest;

                /**
                 * Creates a plain object from an UpdateAssetRequest message. Also converts values to other types if specified.
                 * @param message UpdateAssetRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.cms_admin.UpdateAssetRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this UpdateAssetRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for UpdateAssetRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of an UpdateAssetResponse. */
            interface IUpdateAssetResponse {

                /** UpdateAssetResponse success */
                success?: (boolean|null);
            }

            /** Represents an UpdateAssetResponse. */
            class UpdateAssetResponse implements IUpdateAssetResponse {

                /**
                 * Constructs a new UpdateAssetResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.cms_admin.IUpdateAssetResponse);

                /** UpdateAssetResponse success. */
                public success: boolean;

                /**
                 * Encodes the specified UpdateAssetResponse message. Does not implicitly {@link trpc.video_media.cms_admin.UpdateAssetResponse.verify|verify} messages.
                 * @param message UpdateAssetResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.cms_admin.IUpdateAssetResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes an UpdateAssetResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns UpdateAssetResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.cms_admin.UpdateAssetResponse;

                /**
                 * Verifies an UpdateAssetResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates an UpdateAssetResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns UpdateAssetResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.cms_admin.UpdateAssetResponse;

                /**
                 * Creates a plain object from an UpdateAssetResponse message. Also converts values to other types if specified.
                 * @param message UpdateAssetResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.cms_admin.UpdateAssetResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this UpdateAssetResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for UpdateAssetResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a DeleteAssetRequest. */
            interface IDeleteAssetRequest {

                /** DeleteAssetRequest asset_id */
                asset_id?: (string|null);
            }

            /** Represents a DeleteAssetRequest. */
            class DeleteAssetRequest implements IDeleteAssetRequest {

                /**
                 * Constructs a new DeleteAssetRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.cms_admin.IDeleteAssetRequest);

                /** DeleteAssetRequest asset_id. */
                public asset_id: string;

                /**
                 * Encodes the specified DeleteAssetRequest message. Does not implicitly {@link trpc.video_media.cms_admin.DeleteAssetRequest.verify|verify} messages.
                 * @param message DeleteAssetRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.cms_admin.IDeleteAssetRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a DeleteAssetRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns DeleteAssetRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.cms_admin.DeleteAssetRequest;

                /**
                 * Verifies a DeleteAssetRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a DeleteAssetRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns DeleteAssetRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.cms_admin.DeleteAssetRequest;

                /**
                 * Creates a plain object from a DeleteAssetRequest message. Also converts values to other types if specified.
                 * @param message DeleteAssetRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.cms_admin.DeleteAssetRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this DeleteAssetRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for DeleteAssetRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a DeleteAssetResponse. */
            interface IDeleteAssetResponse {

                /** DeleteAssetResponse success */
                success?: (boolean|null);
            }

            /** Represents a DeleteAssetResponse. */
            class DeleteAssetResponse implements IDeleteAssetResponse {

                /**
                 * Constructs a new DeleteAssetResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.cms_admin.IDeleteAssetResponse);

                /** DeleteAssetResponse success. */
                public success: boolean;

                /**
                 * Encodes the specified DeleteAssetResponse message. Does not implicitly {@link trpc.video_media.cms_admin.DeleteAssetResponse.verify|verify} messages.
                 * @param message DeleteAssetResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.cms_admin.IDeleteAssetResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a DeleteAssetResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns DeleteAssetResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.cms_admin.DeleteAssetResponse;

                /**
                 * Verifies a DeleteAssetResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a DeleteAssetResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns DeleteAssetResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.cms_admin.DeleteAssetResponse;

                /**
                 * Creates a plain object from a DeleteAssetResponse message. Also converts values to other types if specified.
                 * @param message DeleteAssetResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.cms_admin.DeleteAssetResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this DeleteAssetResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for DeleteAssetResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a ListAssetsRequest. */
            interface IListAssetsRequest {

                /** ListAssetsRequest page */
                page?: (number|null);

                /** ListAssetsRequest page_size */
                page_size?: (number|null);

                /** ListAssetsRequest asset_type */
                asset_type?: (trpc.video_media.cms_admin.AssetType|null);

                /** ListAssetsRequest tag */
                tag?: (string|null);

                /** ListAssetsRequest project_id */
                project_id?: (string|null);
            }

            /** Represents a ListAssetsRequest. */
            class ListAssetsRequest implements IListAssetsRequest {

                /**
                 * Constructs a new ListAssetsRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.cms_admin.IListAssetsRequest);

                /** ListAssetsRequest page. */
                public page: number;

                /** ListAssetsRequest page_size. */
                public page_size: number;

                /** ListAssetsRequest asset_type. */
                public asset_type: trpc.video_media.cms_admin.AssetType;

                /** ListAssetsRequest tag. */
                public tag: string;

                /** ListAssetsRequest project_id. */
                public project_id: string;

                /**
                 * Encodes the specified ListAssetsRequest message. Does not implicitly {@link trpc.video_media.cms_admin.ListAssetsRequest.verify|verify} messages.
                 * @param message ListAssetsRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.cms_admin.IListAssetsRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a ListAssetsRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns ListAssetsRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.cms_admin.ListAssetsRequest;

                /**
                 * Verifies a ListAssetsRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a ListAssetsRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns ListAssetsRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.cms_admin.ListAssetsRequest;

                /**
                 * Creates a plain object from a ListAssetsRequest message. Also converts values to other types if specified.
                 * @param message ListAssetsRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.cms_admin.ListAssetsRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this ListAssetsRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for ListAssetsRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a ListAssetsResponse. */
            interface IListAssetsResponse {

                /** ListAssetsResponse total */
                total?: (number|null);

                /** ListAssetsResponse assets */
                assets?: (trpc.video_media.cms_admin.IAsset[]|null);
            }

            /** Represents a ListAssetsResponse. */
            class ListAssetsResponse implements IListAssetsResponse {

                /**
                 * Constructs a new ListAssetsResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.cms_admin.IListAssetsResponse);

                /** ListAssetsResponse total. */
                public total: number;

                /** ListAssetsResponse assets. */
                public assets: trpc.video_media.cms_admin.IAsset[];

                /**
                 * Encodes the specified ListAssetsResponse message. Does not implicitly {@link trpc.video_media.cms_admin.ListAssetsResponse.verify|verify} messages.
                 * @param message ListAssetsResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.cms_admin.IListAssetsResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a ListAssetsResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns ListAssetsResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.cms_admin.ListAssetsResponse;

                /**
                 * Verifies a ListAssetsResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a ListAssetsResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns ListAssetsResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.cms_admin.ListAssetsResponse;

                /**
                 * Creates a plain object from a ListAssetsResponse message. Also converts values to other types if specified.
                 * @param message ListAssetsResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.cms_admin.ListAssetsResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this ListAssetsResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for ListAssetsResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }
        }
    }
}

/** Namespace validate. */
export namespace validate {

    /** Properties of a FieldRules. */
    interface IFieldRules {

        /** FieldRules message */
        message?: (validate.IMessageRules|null);

        /** FieldRules float */
        float?: (validate.IFloatRules|null);

        /** FieldRules double */
        double?: (validate.IDoubleRules|null);

        /** FieldRules int32 */
        int32?: (validate.IInt32Rules|null);

        /** FieldRules int64 */
        int64?: (validate.IInt64Rules|null);

        /** FieldRules uint32 */
        uint32?: (validate.IUInt32Rules|null);

        /** FieldRules uint64 */
        uint64?: (validate.IUInt64Rules|null);

        /** FieldRules sint32 */
        sint32?: (validate.ISInt32Rules|null);

        /** FieldRules sint64 */
        sint64?: (validate.ISInt64Rules|null);

        /** FieldRules fixed32 */
        fixed32?: (validate.IFixed32Rules|null);

        /** FieldRules fixed64 */
        fixed64?: (validate.IFixed64Rules|null);

        /** FieldRules sfixed32 */
        sfixed32?: (validate.ISFixed32Rules|null);

        /** FieldRules sfixed64 */
        sfixed64?: (validate.ISFixed64Rules|null);

        /** FieldRules bool */
        bool?: (validate.IBoolRules|null);

        /** FieldRules string */
        string?: (validate.IStringRules|null);

        /** FieldRules bytes */
        bytes?: (validate.IBytesRules|null);

        /** FieldRules enum */
        "enum"?: (validate.IEnumRules|null);

        /** FieldRules repeated */
        repeated?: (validate.IRepeatedRules|null);

        /** FieldRules map */
        map?: (validate.IMapRules|null);

        /** FieldRules any */
        any?: (validate.IAnyRules|null);

        /** FieldRules duration */
        duration?: (validate.IDurationRules|null);

        /** FieldRules timestamp */
        timestamp?: (validate.ITimestampRules|null);
    }

    /** Represents a FieldRules. */
    class FieldRules implements IFieldRules {

        /**
         * Constructs a new FieldRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IFieldRules);

        /** FieldRules message. */
        public message?: (validate.IMessageRules|null);

        /** FieldRules float. */
        public float?: (validate.IFloatRules|null);

        /** FieldRules double. */
        public double?: (validate.IDoubleRules|null);

        /** FieldRules int32. */
        public int32?: (validate.IInt32Rules|null);

        /** FieldRules int64. */
        public int64?: (validate.IInt64Rules|null);

        /** FieldRules uint32. */
        public uint32?: (validate.IUInt32Rules|null);

        /** FieldRules uint64. */
        public uint64?: (validate.IUInt64Rules|null);

        /** FieldRules sint32. */
        public sint32?: (validate.ISInt32Rules|null);

        /** FieldRules sint64. */
        public sint64?: (validate.ISInt64Rules|null);

        /** FieldRules fixed32. */
        public fixed32?: (validate.IFixed32Rules|null);

        /** FieldRules fixed64. */
        public fixed64?: (validate.IFixed64Rules|null);

        /** FieldRules sfixed32. */
        public sfixed32?: (validate.ISFixed32Rules|null);

        /** FieldRules sfixed64. */
        public sfixed64?: (validate.ISFixed64Rules|null);

        /** FieldRules bool. */
        public bool?: (validate.IBoolRules|null);

        /** FieldRules string. */
        public string?: (validate.IStringRules|null);

        /** FieldRules bytes. */
        public bytes?: (validate.IBytesRules|null);

        /** FieldRules enum. */
        public enum?: (validate.IEnumRules|null);

        /** FieldRules repeated. */
        public repeated?: (validate.IRepeatedRules|null);

        /** FieldRules map. */
        public map?: (validate.IMapRules|null);

        /** FieldRules any. */
        public any?: (validate.IAnyRules|null);

        /** FieldRules duration. */
        public duration?: (validate.IDurationRules|null);

        /** FieldRules timestamp. */
        public timestamp?: (validate.ITimestampRules|null);

        /** FieldRules type. */
        public type?: ("float"|"double"|"int32"|"int64"|"uint32"|"uint64"|"sint32"|"sint64"|"fixed32"|"fixed64"|"sfixed32"|"sfixed64"|"bool"|"string"|"bytes"|"enum"|"repeated"|"map"|"any"|"duration"|"timestamp");

        /**
         * Encodes the specified FieldRules message. Does not implicitly {@link validate.FieldRules.verify|verify} messages.
         * @param message FieldRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IFieldRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a FieldRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns FieldRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.FieldRules;

        /**
         * Verifies a FieldRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a FieldRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns FieldRules
         */
        public static fromObject(object: { [k: string]: any }): validate.FieldRules;

        /**
         * Creates a plain object from a FieldRules message. Also converts values to other types if specified.
         * @param message FieldRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.FieldRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this FieldRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for FieldRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a FloatRules. */
    interface IFloatRules {

        /** FloatRules const */
        "const"?: (number|null);

        /** FloatRules lt */
        lt?: (number|null);

        /** FloatRules lte */
        lte?: (number|null);

        /** FloatRules gt */
        gt?: (number|null);

        /** FloatRules gte */
        gte?: (number|null);

        /** FloatRules in */
        "in"?: (number[]|null);

        /** FloatRules not_in */
        not_in?: (number[]|null);

        /** FloatRules ignore_empty */
        ignore_empty?: (boolean|null);
    }

    /** Represents a FloatRules. */
    class FloatRules implements IFloatRules {

        /**
         * Constructs a new FloatRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IFloatRules);

        /** FloatRules const. */
        public const: number;

        /** FloatRules lt. */
        public lt: number;

        /** FloatRules lte. */
        public lte: number;

        /** FloatRules gt. */
        public gt: number;

        /** FloatRules gte. */
        public gte: number;

        /** FloatRules in. */
        public in: number[];

        /** FloatRules not_in. */
        public not_in: number[];

        /** FloatRules ignore_empty. */
        public ignore_empty: boolean;

        /**
         * Encodes the specified FloatRules message. Does not implicitly {@link validate.FloatRules.verify|verify} messages.
         * @param message FloatRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IFloatRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a FloatRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns FloatRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.FloatRules;

        /**
         * Verifies a FloatRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a FloatRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns FloatRules
         */
        public static fromObject(object: { [k: string]: any }): validate.FloatRules;

        /**
         * Creates a plain object from a FloatRules message. Also converts values to other types if specified.
         * @param message FloatRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.FloatRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this FloatRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for FloatRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a DoubleRules. */
    interface IDoubleRules {

        /** DoubleRules const */
        "const"?: (number|null);

        /** DoubleRules lt */
        lt?: (number|null);

        /** DoubleRules lte */
        lte?: (number|null);

        /** DoubleRules gt */
        gt?: (number|null);

        /** DoubleRules gte */
        gte?: (number|null);

        /** DoubleRules in */
        "in"?: (number[]|null);

        /** DoubleRules not_in */
        not_in?: (number[]|null);

        /** DoubleRules ignore_empty */
        ignore_empty?: (boolean|null);
    }

    /** Represents a DoubleRules. */
    class DoubleRules implements IDoubleRules {

        /**
         * Constructs a new DoubleRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IDoubleRules);

        /** DoubleRules const. */
        public const: number;

        /** DoubleRules lt. */
        public lt: number;

        /** DoubleRules lte. */
        public lte: number;

        /** DoubleRules gt. */
        public gt: number;

        /** DoubleRules gte. */
        public gte: number;

        /** DoubleRules in. */
        public in: number[];

        /** DoubleRules not_in. */
        public not_in: number[];

        /** DoubleRules ignore_empty. */
        public ignore_empty: boolean;

        /**
         * Encodes the specified DoubleRules message. Does not implicitly {@link validate.DoubleRules.verify|verify} messages.
         * @param message DoubleRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IDoubleRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a DoubleRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns DoubleRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.DoubleRules;

        /**
         * Verifies a DoubleRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a DoubleRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns DoubleRules
         */
        public static fromObject(object: { [k: string]: any }): validate.DoubleRules;

        /**
         * Creates a plain object from a DoubleRules message. Also converts values to other types if specified.
         * @param message DoubleRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.DoubleRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this DoubleRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for DoubleRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of an Int32Rules. */
    interface IInt32Rules {

        /** Int32Rules const */
        "const"?: (number|null);

        /** Int32Rules lt */
        lt?: (number|null);

        /** Int32Rules lte */
        lte?: (number|null);

        /** Int32Rules gt */
        gt?: (number|null);

        /** Int32Rules gte */
        gte?: (number|null);

        /** Int32Rules in */
        "in"?: (number[]|null);

        /** Int32Rules not_in */
        not_in?: (number[]|null);

        /** Int32Rules ignore_empty */
        ignore_empty?: (boolean|null);
    }

    /** Represents an Int32Rules. */
    class Int32Rules implements IInt32Rules {

        /**
         * Constructs a new Int32Rules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IInt32Rules);

        /** Int32Rules const. */
        public const: number;

        /** Int32Rules lt. */
        public lt: number;

        /** Int32Rules lte. */
        public lte: number;

        /** Int32Rules gt. */
        public gt: number;

        /** Int32Rules gte. */
        public gte: number;

        /** Int32Rules in. */
        public in: number[];

        /** Int32Rules not_in. */
        public not_in: number[];

        /** Int32Rules ignore_empty. */
        public ignore_empty: boolean;

        /**
         * Encodes the specified Int32Rules message. Does not implicitly {@link validate.Int32Rules.verify|verify} messages.
         * @param message Int32Rules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IInt32Rules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an Int32Rules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Int32Rules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.Int32Rules;

        /**
         * Verifies an Int32Rules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an Int32Rules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Int32Rules
         */
        public static fromObject(object: { [k: string]: any }): validate.Int32Rules;

        /**
         * Creates a plain object from an Int32Rules message. Also converts values to other types if specified.
         * @param message Int32Rules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.Int32Rules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Int32Rules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for Int32Rules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of an Int64Rules. */
    interface IInt64Rules {

        /** Int64Rules const */
        "const"?: (number|Long|null);

        /** Int64Rules lt */
        lt?: (number|Long|null);

        /** Int64Rules lte */
        lte?: (number|Long|null);

        /** Int64Rules gt */
        gt?: (number|Long|null);

        /** Int64Rules gte */
        gte?: (number|Long|null);

        /** Int64Rules in */
        "in"?: ((number|Long)[]|null);

        /** Int64Rules not_in */
        not_in?: ((number|Long)[]|null);

        /** Int64Rules ignore_empty */
        ignore_empty?: (boolean|null);
    }

    /** Represents an Int64Rules. */
    class Int64Rules implements IInt64Rules {

        /**
         * Constructs a new Int64Rules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IInt64Rules);

        /** Int64Rules const. */
        public const: (number|Long);

        /** Int64Rules lt. */
        public lt: (number|Long);

        /** Int64Rules lte. */
        public lte: (number|Long);

        /** Int64Rules gt. */
        public gt: (number|Long);

        /** Int64Rules gte. */
        public gte: (number|Long);

        /** Int64Rules in. */
        public in: (number|Long)[];

        /** Int64Rules not_in. */
        public not_in: (number|Long)[];

        /** Int64Rules ignore_empty. */
        public ignore_empty: boolean;

        /**
         * Encodes the specified Int64Rules message. Does not implicitly {@link validate.Int64Rules.verify|verify} messages.
         * @param message Int64Rules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IInt64Rules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an Int64Rules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Int64Rules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.Int64Rules;

        /**
         * Verifies an Int64Rules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an Int64Rules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Int64Rules
         */
        public static fromObject(object: { [k: string]: any }): validate.Int64Rules;

        /**
         * Creates a plain object from an Int64Rules message. Also converts values to other types if specified.
         * @param message Int64Rules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.Int64Rules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Int64Rules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for Int64Rules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a UInt32Rules. */
    interface IUInt32Rules {

        /** UInt32Rules const */
        "const"?: (number|null);

        /** UInt32Rules lt */
        lt?: (number|null);

        /** UInt32Rules lte */
        lte?: (number|null);

        /** UInt32Rules gt */
        gt?: (number|null);

        /** UInt32Rules gte */
        gte?: (number|null);

        /** UInt32Rules in */
        "in"?: (number[]|null);

        /** UInt32Rules not_in */
        not_in?: (number[]|null);

        /** UInt32Rules ignore_empty */
        ignore_empty?: (boolean|null);
    }

    /** Represents a UInt32Rules. */
    class UInt32Rules implements IUInt32Rules {

        /**
         * Constructs a new UInt32Rules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IUInt32Rules);

        /** UInt32Rules const. */
        public const: number;

        /** UInt32Rules lt. */
        public lt: number;

        /** UInt32Rules lte. */
        public lte: number;

        /** UInt32Rules gt. */
        public gt: number;

        /** UInt32Rules gte. */
        public gte: number;

        /** UInt32Rules in. */
        public in: number[];

        /** UInt32Rules not_in. */
        public not_in: number[];

        /** UInt32Rules ignore_empty. */
        public ignore_empty: boolean;

        /**
         * Encodes the specified UInt32Rules message. Does not implicitly {@link validate.UInt32Rules.verify|verify} messages.
         * @param message UInt32Rules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IUInt32Rules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a UInt32Rules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns UInt32Rules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.UInt32Rules;

        /**
         * Verifies a UInt32Rules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a UInt32Rules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns UInt32Rules
         */
        public static fromObject(object: { [k: string]: any }): validate.UInt32Rules;

        /**
         * Creates a plain object from a UInt32Rules message. Also converts values to other types if specified.
         * @param message UInt32Rules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.UInt32Rules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this UInt32Rules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for UInt32Rules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a UInt64Rules. */
    interface IUInt64Rules {

        /** UInt64Rules const */
        "const"?: (number|Long|null);

        /** UInt64Rules lt */
        lt?: (number|Long|null);

        /** UInt64Rules lte */
        lte?: (number|Long|null);

        /** UInt64Rules gt */
        gt?: (number|Long|null);

        /** UInt64Rules gte */
        gte?: (number|Long|null);

        /** UInt64Rules in */
        "in"?: ((number|Long)[]|null);

        /** UInt64Rules not_in */
        not_in?: ((number|Long)[]|null);

        /** UInt64Rules ignore_empty */
        ignore_empty?: (boolean|null);
    }

    /** Represents a UInt64Rules. */
    class UInt64Rules implements IUInt64Rules {

        /**
         * Constructs a new UInt64Rules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IUInt64Rules);

        /** UInt64Rules const. */
        public const: (number|Long);

        /** UInt64Rules lt. */
        public lt: (number|Long);

        /** UInt64Rules lte. */
        public lte: (number|Long);

        /** UInt64Rules gt. */
        public gt: (number|Long);

        /** UInt64Rules gte. */
        public gte: (number|Long);

        /** UInt64Rules in. */
        public in: (number|Long)[];

        /** UInt64Rules not_in. */
        public not_in: (number|Long)[];

        /** UInt64Rules ignore_empty. */
        public ignore_empty: boolean;

        /**
         * Encodes the specified UInt64Rules message. Does not implicitly {@link validate.UInt64Rules.verify|verify} messages.
         * @param message UInt64Rules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IUInt64Rules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a UInt64Rules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns UInt64Rules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.UInt64Rules;

        /**
         * Verifies a UInt64Rules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a UInt64Rules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns UInt64Rules
         */
        public static fromObject(object: { [k: string]: any }): validate.UInt64Rules;

        /**
         * Creates a plain object from a UInt64Rules message. Also converts values to other types if specified.
         * @param message UInt64Rules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.UInt64Rules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this UInt64Rules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for UInt64Rules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a SInt32Rules. */
    interface ISInt32Rules {

        /** SInt32Rules const */
        "const"?: (number|null);

        /** SInt32Rules lt */
        lt?: (number|null);

        /** SInt32Rules lte */
        lte?: (number|null);

        /** SInt32Rules gt */
        gt?: (number|null);

        /** SInt32Rules gte */
        gte?: (number|null);

        /** SInt32Rules in */
        "in"?: (number[]|null);

        /** SInt32Rules not_in */
        not_in?: (number[]|null);

        /** SInt32Rules ignore_empty */
        ignore_empty?: (boolean|null);
    }

    /** Represents a SInt32Rules. */
    class SInt32Rules implements ISInt32Rules {

        /**
         * Constructs a new SInt32Rules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.ISInt32Rules);

        /** SInt32Rules const. */
        public const: number;

        /** SInt32Rules lt. */
        public lt: number;

        /** SInt32Rules lte. */
        public lte: number;

        /** SInt32Rules gt. */
        public gt: number;

        /** SInt32Rules gte. */
        public gte: number;

        /** SInt32Rules in. */
        public in: number[];

        /** SInt32Rules not_in. */
        public not_in: number[];

        /** SInt32Rules ignore_empty. */
        public ignore_empty: boolean;

        /**
         * Encodes the specified SInt32Rules message. Does not implicitly {@link validate.SInt32Rules.verify|verify} messages.
         * @param message SInt32Rules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.ISInt32Rules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a SInt32Rules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns SInt32Rules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.SInt32Rules;

        /**
         * Verifies a SInt32Rules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a SInt32Rules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns SInt32Rules
         */
        public static fromObject(object: { [k: string]: any }): validate.SInt32Rules;

        /**
         * Creates a plain object from a SInt32Rules message. Also converts values to other types if specified.
         * @param message SInt32Rules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.SInt32Rules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this SInt32Rules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for SInt32Rules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a SInt64Rules. */
    interface ISInt64Rules {

        /** SInt64Rules const */
        "const"?: (number|Long|null);

        /** SInt64Rules lt */
        lt?: (number|Long|null);

        /** SInt64Rules lte */
        lte?: (number|Long|null);

        /** SInt64Rules gt */
        gt?: (number|Long|null);

        /** SInt64Rules gte */
        gte?: (number|Long|null);

        /** SInt64Rules in */
        "in"?: ((number|Long)[]|null);

        /** SInt64Rules not_in */
        not_in?: ((number|Long)[]|null);

        /** SInt64Rules ignore_empty */
        ignore_empty?: (boolean|null);
    }

    /** Represents a SInt64Rules. */
    class SInt64Rules implements ISInt64Rules {

        /**
         * Constructs a new SInt64Rules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.ISInt64Rules);

        /** SInt64Rules const. */
        public const: (number|Long);

        /** SInt64Rules lt. */
        public lt: (number|Long);

        /** SInt64Rules lte. */
        public lte: (number|Long);

        /** SInt64Rules gt. */
        public gt: (number|Long);

        /** SInt64Rules gte. */
        public gte: (number|Long);

        /** SInt64Rules in. */
        public in: (number|Long)[];

        /** SInt64Rules not_in. */
        public not_in: (number|Long)[];

        /** SInt64Rules ignore_empty. */
        public ignore_empty: boolean;

        /**
         * Encodes the specified SInt64Rules message. Does not implicitly {@link validate.SInt64Rules.verify|verify} messages.
         * @param message SInt64Rules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.ISInt64Rules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a SInt64Rules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns SInt64Rules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.SInt64Rules;

        /**
         * Verifies a SInt64Rules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a SInt64Rules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns SInt64Rules
         */
        public static fromObject(object: { [k: string]: any }): validate.SInt64Rules;

        /**
         * Creates a plain object from a SInt64Rules message. Also converts values to other types if specified.
         * @param message SInt64Rules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.SInt64Rules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this SInt64Rules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for SInt64Rules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a Fixed32Rules. */
    interface IFixed32Rules {

        /** Fixed32Rules const */
        "const"?: (number|null);

        /** Fixed32Rules lt */
        lt?: (number|null);

        /** Fixed32Rules lte */
        lte?: (number|null);

        /** Fixed32Rules gt */
        gt?: (number|null);

        /** Fixed32Rules gte */
        gte?: (number|null);

        /** Fixed32Rules in */
        "in"?: (number[]|null);

        /** Fixed32Rules not_in */
        not_in?: (number[]|null);

        /** Fixed32Rules ignore_empty */
        ignore_empty?: (boolean|null);
    }

    /** Represents a Fixed32Rules. */
    class Fixed32Rules implements IFixed32Rules {

        /**
         * Constructs a new Fixed32Rules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IFixed32Rules);

        /** Fixed32Rules const. */
        public const: number;

        /** Fixed32Rules lt. */
        public lt: number;

        /** Fixed32Rules lte. */
        public lte: number;

        /** Fixed32Rules gt. */
        public gt: number;

        /** Fixed32Rules gte. */
        public gte: number;

        /** Fixed32Rules in. */
        public in: number[];

        /** Fixed32Rules not_in. */
        public not_in: number[];

        /** Fixed32Rules ignore_empty. */
        public ignore_empty: boolean;

        /**
         * Encodes the specified Fixed32Rules message. Does not implicitly {@link validate.Fixed32Rules.verify|verify} messages.
         * @param message Fixed32Rules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IFixed32Rules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a Fixed32Rules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Fixed32Rules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.Fixed32Rules;

        /**
         * Verifies a Fixed32Rules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a Fixed32Rules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Fixed32Rules
         */
        public static fromObject(object: { [k: string]: any }): validate.Fixed32Rules;

        /**
         * Creates a plain object from a Fixed32Rules message. Also converts values to other types if specified.
         * @param message Fixed32Rules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.Fixed32Rules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Fixed32Rules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for Fixed32Rules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a Fixed64Rules. */
    interface IFixed64Rules {

        /** Fixed64Rules const */
        "const"?: (number|Long|null);

        /** Fixed64Rules lt */
        lt?: (number|Long|null);

        /** Fixed64Rules lte */
        lte?: (number|Long|null);

        /** Fixed64Rules gt */
        gt?: (number|Long|null);

        /** Fixed64Rules gte */
        gte?: (number|Long|null);

        /** Fixed64Rules in */
        "in"?: ((number|Long)[]|null);

        /** Fixed64Rules not_in */
        not_in?: ((number|Long)[]|null);

        /** Fixed64Rules ignore_empty */
        ignore_empty?: (boolean|null);
    }

    /** Represents a Fixed64Rules. */
    class Fixed64Rules implements IFixed64Rules {

        /**
         * Constructs a new Fixed64Rules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IFixed64Rules);

        /** Fixed64Rules const. */
        public const: (number|Long);

        /** Fixed64Rules lt. */
        public lt: (number|Long);

        /** Fixed64Rules lte. */
        public lte: (number|Long);

        /** Fixed64Rules gt. */
        public gt: (number|Long);

        /** Fixed64Rules gte. */
        public gte: (number|Long);

        /** Fixed64Rules in. */
        public in: (number|Long)[];

        /** Fixed64Rules not_in. */
        public not_in: (number|Long)[];

        /** Fixed64Rules ignore_empty. */
        public ignore_empty: boolean;

        /**
         * Encodes the specified Fixed64Rules message. Does not implicitly {@link validate.Fixed64Rules.verify|verify} messages.
         * @param message Fixed64Rules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IFixed64Rules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a Fixed64Rules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Fixed64Rules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.Fixed64Rules;

        /**
         * Verifies a Fixed64Rules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a Fixed64Rules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Fixed64Rules
         */
        public static fromObject(object: { [k: string]: any }): validate.Fixed64Rules;

        /**
         * Creates a plain object from a Fixed64Rules message. Also converts values to other types if specified.
         * @param message Fixed64Rules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.Fixed64Rules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Fixed64Rules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for Fixed64Rules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a SFixed32Rules. */
    interface ISFixed32Rules {

        /** SFixed32Rules const */
        "const"?: (number|null);

        /** SFixed32Rules lt */
        lt?: (number|null);

        /** SFixed32Rules lte */
        lte?: (number|null);

        /** SFixed32Rules gt */
        gt?: (number|null);

        /** SFixed32Rules gte */
        gte?: (number|null);

        /** SFixed32Rules in */
        "in"?: (number[]|null);

        /** SFixed32Rules not_in */
        not_in?: (number[]|null);

        /** SFixed32Rules ignore_empty */
        ignore_empty?: (boolean|null);
    }

    /** Represents a SFixed32Rules. */
    class SFixed32Rules implements ISFixed32Rules {

        /**
         * Constructs a new SFixed32Rules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.ISFixed32Rules);

        /** SFixed32Rules const. */
        public const: number;

        /** SFixed32Rules lt. */
        public lt: number;

        /** SFixed32Rules lte. */
        public lte: number;

        /** SFixed32Rules gt. */
        public gt: number;

        /** SFixed32Rules gte. */
        public gte: number;

        /** SFixed32Rules in. */
        public in: number[];

        /** SFixed32Rules not_in. */
        public not_in: number[];

        /** SFixed32Rules ignore_empty. */
        public ignore_empty: boolean;

        /**
         * Encodes the specified SFixed32Rules message. Does not implicitly {@link validate.SFixed32Rules.verify|verify} messages.
         * @param message SFixed32Rules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.ISFixed32Rules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a SFixed32Rules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns SFixed32Rules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.SFixed32Rules;

        /**
         * Verifies a SFixed32Rules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a SFixed32Rules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns SFixed32Rules
         */
        public static fromObject(object: { [k: string]: any }): validate.SFixed32Rules;

        /**
         * Creates a plain object from a SFixed32Rules message. Also converts values to other types if specified.
         * @param message SFixed32Rules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.SFixed32Rules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this SFixed32Rules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for SFixed32Rules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a SFixed64Rules. */
    interface ISFixed64Rules {

        /** SFixed64Rules const */
        "const"?: (number|Long|null);

        /** SFixed64Rules lt */
        lt?: (number|Long|null);

        /** SFixed64Rules lte */
        lte?: (number|Long|null);

        /** SFixed64Rules gt */
        gt?: (number|Long|null);

        /** SFixed64Rules gte */
        gte?: (number|Long|null);

        /** SFixed64Rules in */
        "in"?: ((number|Long)[]|null);

        /** SFixed64Rules not_in */
        not_in?: ((number|Long)[]|null);

        /** SFixed64Rules ignore_empty */
        ignore_empty?: (boolean|null);
    }

    /** Represents a SFixed64Rules. */
    class SFixed64Rules implements ISFixed64Rules {

        /**
         * Constructs a new SFixed64Rules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.ISFixed64Rules);

        /** SFixed64Rules const. */
        public const: (number|Long);

        /** SFixed64Rules lt. */
        public lt: (number|Long);

        /** SFixed64Rules lte. */
        public lte: (number|Long);

        /** SFixed64Rules gt. */
        public gt: (number|Long);

        /** SFixed64Rules gte. */
        public gte: (number|Long);

        /** SFixed64Rules in. */
        public in: (number|Long)[];

        /** SFixed64Rules not_in. */
        public not_in: (number|Long)[];

        /** SFixed64Rules ignore_empty. */
        public ignore_empty: boolean;

        /**
         * Encodes the specified SFixed64Rules message. Does not implicitly {@link validate.SFixed64Rules.verify|verify} messages.
         * @param message SFixed64Rules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.ISFixed64Rules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a SFixed64Rules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns SFixed64Rules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.SFixed64Rules;

        /**
         * Verifies a SFixed64Rules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a SFixed64Rules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns SFixed64Rules
         */
        public static fromObject(object: { [k: string]: any }): validate.SFixed64Rules;

        /**
         * Creates a plain object from a SFixed64Rules message. Also converts values to other types if specified.
         * @param message SFixed64Rules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.SFixed64Rules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this SFixed64Rules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for SFixed64Rules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a BoolRules. */
    interface IBoolRules {

        /** BoolRules const */
        "const"?: (boolean|null);
    }

    /** Represents a BoolRules. */
    class BoolRules implements IBoolRules {

        /**
         * Constructs a new BoolRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IBoolRules);

        /** BoolRules const. */
        public const: boolean;

        /**
         * Encodes the specified BoolRules message. Does not implicitly {@link validate.BoolRules.verify|verify} messages.
         * @param message BoolRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IBoolRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a BoolRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns BoolRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.BoolRules;

        /**
         * Verifies a BoolRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a BoolRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns BoolRules
         */
        public static fromObject(object: { [k: string]: any }): validate.BoolRules;

        /**
         * Creates a plain object from a BoolRules message. Also converts values to other types if specified.
         * @param message BoolRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.BoolRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this BoolRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for BoolRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a StringRules. */
    interface IStringRules {

        /** StringRules const */
        "const"?: (string|null);

        /** StringRules len */
        len?: (number|Long|null);

        /** StringRules min_len */
        min_len?: (number|Long|null);

        /** StringRules max_len */
        max_len?: (number|Long|null);

        /** StringRules len_bytes */
        len_bytes?: (number|Long|null);

        /** StringRules min_bytes */
        min_bytes?: (number|Long|null);

        /** StringRules max_bytes */
        max_bytes?: (number|Long|null);

        /** StringRules pattern */
        pattern?: (string|null);

        /** StringRules prefix */
        prefix?: (string|null);

        /** StringRules suffix */
        suffix?: (string|null);

        /** StringRules contains */
        contains?: (string|null);

        /** StringRules not_contains */
        not_contains?: (string|null);

        /** StringRules in */
        "in"?: (string[]|null);

        /** StringRules not_in */
        not_in?: (string[]|null);

        /** StringRules email */
        email?: (boolean|null);

        /** StringRules hostname */
        hostname?: (boolean|null);

        /** StringRules ip */
        ip?: (boolean|null);

        /** StringRules ipv4 */
        ipv4?: (boolean|null);

        /** StringRules ipv6 */
        ipv6?: (boolean|null);

        /** StringRules uri */
        uri?: (boolean|null);

        /** StringRules uri_ref */
        uri_ref?: (boolean|null);

        /** StringRules address */
        address?: (boolean|null);

        /** StringRules uuid */
        uuid?: (boolean|null);

        /** StringRules well_known_regex */
        well_known_regex?: (validate.KnownRegex|null);

        /** StringRules alphabets */
        alphabets?: (boolean|null);

        /** StringRules alphanums */
        alphanums?: (boolean|null);

        /** StringRules lowercase */
        lowercase?: (boolean|null);

        /** StringRules tsecstr */
        tsecstr?: (boolean|null);

        /** StringRules json */
        json?: (boolean|null);

        /** StringRules strict */
        strict?: (boolean|null);

        /** StringRules ignore_empty */
        ignore_empty?: (boolean|null);
    }

    /** Represents a StringRules. */
    class StringRules implements IStringRules {

        /**
         * Constructs a new StringRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IStringRules);

        /** StringRules const. */
        public const: string;

        /** StringRules len. */
        public len: (number|Long);

        /** StringRules min_len. */
        public min_len: (number|Long);

        /** StringRules max_len. */
        public max_len: (number|Long);

        /** StringRules len_bytes. */
        public len_bytes: (number|Long);

        /** StringRules min_bytes. */
        public min_bytes: (number|Long);

        /** StringRules max_bytes. */
        public max_bytes: (number|Long);

        /** StringRules pattern. */
        public pattern: string;

        /** StringRules prefix. */
        public prefix: string;

        /** StringRules suffix. */
        public suffix: string;

        /** StringRules contains. */
        public contains: string;

        /** StringRules not_contains. */
        public not_contains: string;

        /** StringRules in. */
        public in: string[];

        /** StringRules not_in. */
        public not_in: string[];

        /** StringRules email. */
        public email?: (boolean|null);

        /** StringRules hostname. */
        public hostname?: (boolean|null);

        /** StringRules ip. */
        public ip?: (boolean|null);

        /** StringRules ipv4. */
        public ipv4?: (boolean|null);

        /** StringRules ipv6. */
        public ipv6?: (boolean|null);

        /** StringRules uri. */
        public uri?: (boolean|null);

        /** StringRules uri_ref. */
        public uri_ref?: (boolean|null);

        /** StringRules address. */
        public address?: (boolean|null);

        /** StringRules uuid. */
        public uuid?: (boolean|null);

        /** StringRules well_known_regex. */
        public well_known_regex?: (validate.KnownRegex|null);

        /** StringRules alphabets. */
        public alphabets?: (boolean|null);

        /** StringRules alphanums. */
        public alphanums?: (boolean|null);

        /** StringRules lowercase. */
        public lowercase?: (boolean|null);

        /** StringRules tsecstr. */
        public tsecstr?: (boolean|null);

        /** StringRules json. */
        public json?: (boolean|null);

        /** StringRules strict. */
        public strict: boolean;

        /** StringRules ignore_empty. */
        public ignore_empty: boolean;

        /** StringRules well_known. */
        public well_known?: ("email"|"hostname"|"ip"|"ipv4"|"ipv6"|"uri"|"uri_ref"|"address"|"uuid"|"well_known_regex"|"alphabets"|"alphanums"|"lowercase"|"tsecstr"|"json");

        /**
         * Encodes the specified StringRules message. Does not implicitly {@link validate.StringRules.verify|verify} messages.
         * @param message StringRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IStringRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a StringRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns StringRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.StringRules;

        /**
         * Verifies a StringRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a StringRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns StringRules
         */
        public static fromObject(object: { [k: string]: any }): validate.StringRules;

        /**
         * Creates a plain object from a StringRules message. Also converts values to other types if specified.
         * @param message StringRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.StringRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this StringRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for StringRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** KnownRegex enum. */
    enum KnownRegex {
        UNKNOWN = 0,
        HTTP_HEADER_NAME = 1,
        HTTP_HEADER_VALUE = 2
    }

    /** Properties of a BytesRules. */
    interface IBytesRules {

        /** BytesRules const */
        "const"?: (Uint8Array|null);

        /** BytesRules len */
        len?: (number|Long|null);

        /** BytesRules min_len */
        min_len?: (number|Long|null);

        /** BytesRules max_len */
        max_len?: (number|Long|null);

        /** BytesRules pattern */
        pattern?: (string|null);

        /** BytesRules prefix */
        prefix?: (Uint8Array|null);

        /** BytesRules suffix */
        suffix?: (Uint8Array|null);

        /** BytesRules contains */
        contains?: (Uint8Array|null);

        /** BytesRules in */
        "in"?: (Uint8Array[]|null);

        /** BytesRules not_in */
        not_in?: (Uint8Array[]|null);

        /** BytesRules ip */
        ip?: (boolean|null);

        /** BytesRules ipv4 */
        ipv4?: (boolean|null);

        /** BytesRules ipv6 */
        ipv6?: (boolean|null);

        /** BytesRules ignore_empty */
        ignore_empty?: (boolean|null);
    }

    /** Represents a BytesRules. */
    class BytesRules implements IBytesRules {

        /**
         * Constructs a new BytesRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IBytesRules);

        /** BytesRules const. */
        public const: Uint8Array;

        /** BytesRules len. */
        public len: (number|Long);

        /** BytesRules min_len. */
        public min_len: (number|Long);

        /** BytesRules max_len. */
        public max_len: (number|Long);

        /** BytesRules pattern. */
        public pattern: string;

        /** BytesRules prefix. */
        public prefix: Uint8Array;

        /** BytesRules suffix. */
        public suffix: Uint8Array;

        /** BytesRules contains. */
        public contains: Uint8Array;

        /** BytesRules in. */
        public in: Uint8Array[];

        /** BytesRules not_in. */
        public not_in: Uint8Array[];

        /** BytesRules ip. */
        public ip?: (boolean|null);

        /** BytesRules ipv4. */
        public ipv4?: (boolean|null);

        /** BytesRules ipv6. */
        public ipv6?: (boolean|null);

        /** BytesRules ignore_empty. */
        public ignore_empty: boolean;

        /** BytesRules well_known. */
        public well_known?: ("ip"|"ipv4"|"ipv6");

        /**
         * Encodes the specified BytesRules message. Does not implicitly {@link validate.BytesRules.verify|verify} messages.
         * @param message BytesRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IBytesRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a BytesRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns BytesRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.BytesRules;

        /**
         * Verifies a BytesRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a BytesRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns BytesRules
         */
        public static fromObject(object: { [k: string]: any }): validate.BytesRules;

        /**
         * Creates a plain object from a BytesRules message. Also converts values to other types if specified.
         * @param message BytesRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.BytesRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this BytesRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for BytesRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of an EnumRules. */
    interface IEnumRules {

        /** EnumRules const */
        "const"?: (number|null);

        /** EnumRules defined_only */
        defined_only?: (boolean|null);

        /** EnumRules in */
        "in"?: (number[]|null);

        /** EnumRules not_in */
        not_in?: (number[]|null);
    }

    /** Represents an EnumRules. */
    class EnumRules implements IEnumRules {

        /**
         * Constructs a new EnumRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IEnumRules);

        /** EnumRules const. */
        public const: number;

        /** EnumRules defined_only. */
        public defined_only: boolean;

        /** EnumRules in. */
        public in: number[];

        /** EnumRules not_in. */
        public not_in: number[];

        /**
         * Encodes the specified EnumRules message. Does not implicitly {@link validate.EnumRules.verify|verify} messages.
         * @param message EnumRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IEnumRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an EnumRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns EnumRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.EnumRules;

        /**
         * Verifies an EnumRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an EnumRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns EnumRules
         */
        public static fromObject(object: { [k: string]: any }): validate.EnumRules;

        /**
         * Creates a plain object from an EnumRules message. Also converts values to other types if specified.
         * @param message EnumRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.EnumRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this EnumRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for EnumRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a MessageRules. */
    interface IMessageRules {

        /** MessageRules skip */
        skip?: (boolean|null);

        /** MessageRules required */
        required?: (boolean|null);
    }

    /** Represents a MessageRules. */
    class MessageRules implements IMessageRules {

        /**
         * Constructs a new MessageRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IMessageRules);

        /** MessageRules skip. */
        public skip: boolean;

        /** MessageRules required. */
        public required: boolean;

        /**
         * Encodes the specified MessageRules message. Does not implicitly {@link validate.MessageRules.verify|verify} messages.
         * @param message MessageRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IMessageRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a MessageRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns MessageRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.MessageRules;

        /**
         * Verifies a MessageRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a MessageRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns MessageRules
         */
        public static fromObject(object: { [k: string]: any }): validate.MessageRules;

        /**
         * Creates a plain object from a MessageRules message. Also converts values to other types if specified.
         * @param message MessageRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.MessageRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this MessageRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for MessageRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a RepeatedRules. */
    interface IRepeatedRules {

        /** RepeatedRules min_items */
        min_items?: (number|Long|null);

        /** RepeatedRules max_items */
        max_items?: (number|Long|null);

        /** RepeatedRules unique */
        unique?: (boolean|null);

        /** RepeatedRules items */
        items?: (validate.IFieldRules|null);

        /** RepeatedRules ignore_empty */
        ignore_empty?: (boolean|null);
    }

    /** Represents a RepeatedRules. */
    class RepeatedRules implements IRepeatedRules {

        /**
         * Constructs a new RepeatedRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IRepeatedRules);

        /** RepeatedRules min_items. */
        public min_items: (number|Long);

        /** RepeatedRules max_items. */
        public max_items: (number|Long);

        /** RepeatedRules unique. */
        public unique: boolean;

        /** RepeatedRules items. */
        public items?: (validate.IFieldRules|null);

        /** RepeatedRules ignore_empty. */
        public ignore_empty: boolean;

        /**
         * Encodes the specified RepeatedRules message. Does not implicitly {@link validate.RepeatedRules.verify|verify} messages.
         * @param message RepeatedRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IRepeatedRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a RepeatedRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns RepeatedRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.RepeatedRules;

        /**
         * Verifies a RepeatedRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a RepeatedRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns RepeatedRules
         */
        public static fromObject(object: { [k: string]: any }): validate.RepeatedRules;

        /**
         * Creates a plain object from a RepeatedRules message. Also converts values to other types if specified.
         * @param message RepeatedRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.RepeatedRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this RepeatedRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for RepeatedRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a MapRules. */
    interface IMapRules {

        /** MapRules min_pairs */
        min_pairs?: (number|Long|null);

        /** MapRules max_pairs */
        max_pairs?: (number|Long|null);

        /** MapRules no_sparse */
        no_sparse?: (boolean|null);

        /** MapRules keys */
        keys?: (validate.IFieldRules|null);

        /** MapRules values */
        values?: (validate.IFieldRules|null);

        /** MapRules ignore_empty */
        ignore_empty?: (boolean|null);
    }

    /** Represents a MapRules. */
    class MapRules implements IMapRules {

        /**
         * Constructs a new MapRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IMapRules);

        /** MapRules min_pairs. */
        public min_pairs: (number|Long);

        /** MapRules max_pairs. */
        public max_pairs: (number|Long);

        /** MapRules no_sparse. */
        public no_sparse: boolean;

        /** MapRules keys. */
        public keys?: (validate.IFieldRules|null);

        /** MapRules values. */
        public values?: (validate.IFieldRules|null);

        /** MapRules ignore_empty. */
        public ignore_empty: boolean;

        /**
         * Encodes the specified MapRules message. Does not implicitly {@link validate.MapRules.verify|verify} messages.
         * @param message MapRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IMapRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a MapRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns MapRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.MapRules;

        /**
         * Verifies a MapRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a MapRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns MapRules
         */
        public static fromObject(object: { [k: string]: any }): validate.MapRules;

        /**
         * Creates a plain object from a MapRules message. Also converts values to other types if specified.
         * @param message MapRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.MapRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this MapRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for MapRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of an AnyRules. */
    interface IAnyRules {

        /** AnyRules required */
        required?: (boolean|null);

        /** AnyRules in */
        "in"?: (string[]|null);

        /** AnyRules not_in */
        not_in?: (string[]|null);
    }

    /** Represents an AnyRules. */
    class AnyRules implements IAnyRules {

        /**
         * Constructs a new AnyRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IAnyRules);

        /** AnyRules required. */
        public required: boolean;

        /** AnyRules in. */
        public in: string[];

        /** AnyRules not_in. */
        public not_in: string[];

        /**
         * Encodes the specified AnyRules message. Does not implicitly {@link validate.AnyRules.verify|verify} messages.
         * @param message AnyRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IAnyRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an AnyRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns AnyRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.AnyRules;

        /**
         * Verifies an AnyRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an AnyRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns AnyRules
         */
        public static fromObject(object: { [k: string]: any }): validate.AnyRules;

        /**
         * Creates a plain object from an AnyRules message. Also converts values to other types if specified.
         * @param message AnyRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.AnyRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this AnyRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for AnyRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a DurationRules. */
    interface IDurationRules {

        /** DurationRules required */
        required?: (boolean|null);

        /** DurationRules const */
        "const"?: (google.protobuf.IDuration|null);

        /** DurationRules lt */
        lt?: (google.protobuf.IDuration|null);

        /** DurationRules lte */
        lte?: (google.protobuf.IDuration|null);

        /** DurationRules gt */
        gt?: (google.protobuf.IDuration|null);

        /** DurationRules gte */
        gte?: (google.protobuf.IDuration|null);

        /** DurationRules in */
        "in"?: (google.protobuf.IDuration[]|null);

        /** DurationRules not_in */
        not_in?: (google.protobuf.IDuration[]|null);
    }

    /** Represents a DurationRules. */
    class DurationRules implements IDurationRules {

        /**
         * Constructs a new DurationRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IDurationRules);

        /** DurationRules required. */
        public required: boolean;

        /** DurationRules const. */
        public const?: (google.protobuf.IDuration|null);

        /** DurationRules lt. */
        public lt?: (google.protobuf.IDuration|null);

        /** DurationRules lte. */
        public lte?: (google.protobuf.IDuration|null);

        /** DurationRules gt. */
        public gt?: (google.protobuf.IDuration|null);

        /** DurationRules gte. */
        public gte?: (google.protobuf.IDuration|null);

        /** DurationRules in. */
        public in: google.protobuf.IDuration[];

        /** DurationRules not_in. */
        public not_in: google.protobuf.IDuration[];

        /**
         * Encodes the specified DurationRules message. Does not implicitly {@link validate.DurationRules.verify|verify} messages.
         * @param message DurationRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IDurationRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a DurationRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns DurationRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.DurationRules;

        /**
         * Verifies a DurationRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a DurationRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns DurationRules
         */
        public static fromObject(object: { [k: string]: any }): validate.DurationRules;

        /**
         * Creates a plain object from a DurationRules message. Also converts values to other types if specified.
         * @param message DurationRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.DurationRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this DurationRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for DurationRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a TimestampRules. */
    interface ITimestampRules {

        /** TimestampRules required */
        required?: (boolean|null);

        /** TimestampRules const */
        "const"?: (google.protobuf.ITimestamp|null);

        /** TimestampRules lt */
        lt?: (google.protobuf.ITimestamp|null);

        /** TimestampRules lte */
        lte?: (google.protobuf.ITimestamp|null);

        /** TimestampRules gt */
        gt?: (google.protobuf.ITimestamp|null);

        /** TimestampRules gte */
        gte?: (google.protobuf.ITimestamp|null);

        /** TimestampRules lt_now */
        lt_now?: (boolean|null);

        /** TimestampRules gt_now */
        gt_now?: (boolean|null);

        /** TimestampRules within */
        within?: (google.protobuf.IDuration|null);
    }

    /** Represents a TimestampRules. */
    class TimestampRules implements ITimestampRules {

        /**
         * Constructs a new TimestampRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.ITimestampRules);

        /** TimestampRules required. */
        public required: boolean;

        /** TimestampRules const. */
        public const?: (google.protobuf.ITimestamp|null);

        /** TimestampRules lt. */
        public lt?: (google.protobuf.ITimestamp|null);

        /** TimestampRules lte. */
        public lte?: (google.protobuf.ITimestamp|null);

        /** TimestampRules gt. */
        public gt?: (google.protobuf.ITimestamp|null);

        /** TimestampRules gte. */
        public gte?: (google.protobuf.ITimestamp|null);

        /** TimestampRules lt_now. */
        public lt_now: boolean;

        /** TimestampRules gt_now. */
        public gt_now: boolean;

        /** TimestampRules within. */
        public within?: (google.protobuf.IDuration|null);

        /**
         * Encodes the specified TimestampRules message. Does not implicitly {@link validate.TimestampRules.verify|verify} messages.
         * @param message TimestampRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.ITimestampRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a TimestampRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns TimestampRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.TimestampRules;

        /**
         * Verifies a TimestampRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a TimestampRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns TimestampRules
         */
        public static fromObject(object: { [k: string]: any }): validate.TimestampRules;

        /**
         * Creates a plain object from a TimestampRules message. Also converts values to other types if specified.
         * @param message TimestampRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.TimestampRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this TimestampRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for TimestampRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }
}

/** Namespace google. */
export namespace google {

    /** Namespace protobuf. */
    namespace protobuf {

        /** Properties of a FileDescriptorSet. */
        interface IFileDescriptorSet {

            /** FileDescriptorSet file */
            file?: (google.protobuf.IFileDescriptorProto[]|null);
        }

        /** Represents a FileDescriptorSet. */
        class FileDescriptorSet implements IFileDescriptorSet {

            /**
             * Constructs a new FileDescriptorSet.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IFileDescriptorSet);

            /** FileDescriptorSet file. */
            public file: google.protobuf.IFileDescriptorProto[];

            /**
             * Encodes the specified FileDescriptorSet message. Does not implicitly {@link google.protobuf.FileDescriptorSet.verify|verify} messages.
             * @param message FileDescriptorSet message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IFileDescriptorSet, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a FileDescriptorSet message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns FileDescriptorSet
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FileDescriptorSet;

            /**
             * Verifies a FileDescriptorSet message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a FileDescriptorSet message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns FileDescriptorSet
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.FileDescriptorSet;

            /**
             * Creates a plain object from a FileDescriptorSet message. Also converts values to other types if specified.
             * @param message FileDescriptorSet
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.FileDescriptorSet, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this FileDescriptorSet to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for FileDescriptorSet
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Edition enum. */
        enum Edition {
            EDITION_UNKNOWN = 0,
            EDITION_LEGACY = 900,
            EDITION_PROTO2 = 998,
            EDITION_PROTO3 = 999,
            EDITION_2023 = 1000,
            EDITION_2024 = 1001,
            EDITION_1_TEST_ONLY = 1,
            EDITION_2_TEST_ONLY = 2,
            EDITION_99997_TEST_ONLY = 99997,
            EDITION_99998_TEST_ONLY = 99998,
            EDITION_99999_TEST_ONLY = 99999,
            EDITION_MAX = 2147483647
        }

        /** Properties of a FileDescriptorProto. */
        interface IFileDescriptorProto {

            /** FileDescriptorProto name */
            name?: (string|null);

            /** FileDescriptorProto package */
            "package"?: (string|null);

            /** FileDescriptorProto dependency */
            dependency?: (string[]|null);

            /** FileDescriptorProto public_dependency */
            public_dependency?: (number[]|null);

            /** FileDescriptorProto weak_dependency */
            weak_dependency?: (number[]|null);

            /** FileDescriptorProto option_dependency */
            option_dependency?: (string[]|null);

            /** FileDescriptorProto message_type */
            message_type?: (google.protobuf.IDescriptorProto[]|null);

            /** FileDescriptorProto enum_type */
            enum_type?: (google.protobuf.IEnumDescriptorProto[]|null);

            /** FileDescriptorProto service */
            service?: (google.protobuf.IServiceDescriptorProto[]|null);

            /** FileDescriptorProto extension */
            extension?: (google.protobuf.IFieldDescriptorProto[]|null);

            /** FileDescriptorProto options */
            options?: (google.protobuf.IFileOptions|null);

            /** FileDescriptorProto source_code_info */
            source_code_info?: (google.protobuf.ISourceCodeInfo|null);

            /** FileDescriptorProto syntax */
            syntax?: (string|null);

            /** FileDescriptorProto edition */
            edition?: (google.protobuf.Edition|null);
        }

        /** Represents a FileDescriptorProto. */
        class FileDescriptorProto implements IFileDescriptorProto {

            /**
             * Constructs a new FileDescriptorProto.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IFileDescriptorProto);

            /** FileDescriptorProto name. */
            public name: string;

            /** FileDescriptorProto package. */
            public package: string;

            /** FileDescriptorProto dependency. */
            public dependency: string[];

            /** FileDescriptorProto public_dependency. */
            public public_dependency: number[];

            /** FileDescriptorProto weak_dependency. */
            public weak_dependency: number[];

            /** FileDescriptorProto option_dependency. */
            public option_dependency: string[];

            /** FileDescriptorProto message_type. */
            public message_type: google.protobuf.IDescriptorProto[];

            /** FileDescriptorProto enum_type. */
            public enum_type: google.protobuf.IEnumDescriptorProto[];

            /** FileDescriptorProto service. */
            public service: google.protobuf.IServiceDescriptorProto[];

            /** FileDescriptorProto extension. */
            public extension: google.protobuf.IFieldDescriptorProto[];

            /** FileDescriptorProto options. */
            public options?: (google.protobuf.IFileOptions|null);

            /** FileDescriptorProto source_code_info. */
            public source_code_info?: (google.protobuf.ISourceCodeInfo|null);

            /** FileDescriptorProto syntax. */
            public syntax: string;

            /** FileDescriptorProto edition. */
            public edition: google.protobuf.Edition;

            /**
             * Encodes the specified FileDescriptorProto message. Does not implicitly {@link google.protobuf.FileDescriptorProto.verify|verify} messages.
             * @param message FileDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IFileDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a FileDescriptorProto message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns FileDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FileDescriptorProto;

            /**
             * Verifies a FileDescriptorProto message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a FileDescriptorProto message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns FileDescriptorProto
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.FileDescriptorProto;

            /**
             * Creates a plain object from a FileDescriptorProto message. Also converts values to other types if specified.
             * @param message FileDescriptorProto
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.FileDescriptorProto, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this FileDescriptorProto to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for FileDescriptorProto
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a DescriptorProto. */
        interface IDescriptorProto {

            /** DescriptorProto name */
            name?: (string|null);

            /** DescriptorProto field */
            field?: (google.protobuf.IFieldDescriptorProto[]|null);

            /** DescriptorProto extension */
            extension?: (google.protobuf.IFieldDescriptorProto[]|null);

            /** DescriptorProto nested_type */
            nested_type?: (google.protobuf.IDescriptorProto[]|null);

            /** DescriptorProto enum_type */
            enum_type?: (google.protobuf.IEnumDescriptorProto[]|null);

            /** DescriptorProto extension_range */
            extension_range?: (google.protobuf.DescriptorProto.IExtensionRange[]|null);

            /** DescriptorProto oneof_decl */
            oneof_decl?: (google.protobuf.IOneofDescriptorProto[]|null);

            /** DescriptorProto options */
            options?: (google.protobuf.IMessageOptions|null);

            /** DescriptorProto reserved_range */
            reserved_range?: (google.protobuf.DescriptorProto.IReservedRange[]|null);

            /** DescriptorProto reserved_name */
            reserved_name?: (string[]|null);

            /** DescriptorProto visibility */
            visibility?: (google.protobuf.SymbolVisibility|null);
        }

        /** Represents a DescriptorProto. */
        class DescriptorProto implements IDescriptorProto {

            /**
             * Constructs a new DescriptorProto.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IDescriptorProto);

            /** DescriptorProto name. */
            public name: string;

            /** DescriptorProto field. */
            public field: google.protobuf.IFieldDescriptorProto[];

            /** DescriptorProto extension. */
            public extension: google.protobuf.IFieldDescriptorProto[];

            /** DescriptorProto nested_type. */
            public nested_type: google.protobuf.IDescriptorProto[];

            /** DescriptorProto enum_type. */
            public enum_type: google.protobuf.IEnumDescriptorProto[];

            /** DescriptorProto extension_range. */
            public extension_range: google.protobuf.DescriptorProto.IExtensionRange[];

            /** DescriptorProto oneof_decl. */
            public oneof_decl: google.protobuf.IOneofDescriptorProto[];

            /** DescriptorProto options. */
            public options?: (google.protobuf.IMessageOptions|null);

            /** DescriptorProto reserved_range. */
            public reserved_range: google.protobuf.DescriptorProto.IReservedRange[];

            /** DescriptorProto reserved_name. */
            public reserved_name: string[];

            /** DescriptorProto visibility. */
            public visibility: google.protobuf.SymbolVisibility;

            /**
             * Encodes the specified DescriptorProto message. Does not implicitly {@link google.protobuf.DescriptorProto.verify|verify} messages.
             * @param message DescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a DescriptorProto message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns DescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.DescriptorProto;

            /**
             * Verifies a DescriptorProto message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a DescriptorProto message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns DescriptorProto
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.DescriptorProto;

            /**
             * Creates a plain object from a DescriptorProto message. Also converts values to other types if specified.
             * @param message DescriptorProto
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.DescriptorProto, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this DescriptorProto to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for DescriptorProto
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace DescriptorProto {

            /** Properties of an ExtensionRange. */
            interface IExtensionRange {

                /** ExtensionRange start */
                start?: (number|null);

                /** ExtensionRange end */
                end?: (number|null);

                /** ExtensionRange options */
                options?: (google.protobuf.IExtensionRangeOptions|null);
            }

            /** Represents an ExtensionRange. */
            class ExtensionRange implements IExtensionRange {

                /**
                 * Constructs a new ExtensionRange.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.DescriptorProto.IExtensionRange);

                /** ExtensionRange start. */
                public start: number;

                /** ExtensionRange end. */
                public end: number;

                /** ExtensionRange options. */
                public options?: (google.protobuf.IExtensionRangeOptions|null);

                /**
                 * Encodes the specified ExtensionRange message. Does not implicitly {@link google.protobuf.DescriptorProto.ExtensionRange.verify|verify} messages.
                 * @param message ExtensionRange message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.DescriptorProto.IExtensionRange, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes an ExtensionRange message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns ExtensionRange
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.DescriptorProto.ExtensionRange;

                /**
                 * Verifies an ExtensionRange message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates an ExtensionRange message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns ExtensionRange
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.DescriptorProto.ExtensionRange;

                /**
                 * Creates a plain object from an ExtensionRange message. Also converts values to other types if specified.
                 * @param message ExtensionRange
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.DescriptorProto.ExtensionRange, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this ExtensionRange to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for ExtensionRange
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a ReservedRange. */
            interface IReservedRange {

                /** ReservedRange start */
                start?: (number|null);

                /** ReservedRange end */
                end?: (number|null);
            }

            /** Represents a ReservedRange. */
            class ReservedRange implements IReservedRange {

                /**
                 * Constructs a new ReservedRange.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.DescriptorProto.IReservedRange);

                /** ReservedRange start. */
                public start: number;

                /** ReservedRange end. */
                public end: number;

                /**
                 * Encodes the specified ReservedRange message. Does not implicitly {@link google.protobuf.DescriptorProto.ReservedRange.verify|verify} messages.
                 * @param message ReservedRange message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.DescriptorProto.IReservedRange, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a ReservedRange message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns ReservedRange
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.DescriptorProto.ReservedRange;

                /**
                 * Verifies a ReservedRange message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a ReservedRange message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns ReservedRange
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.DescriptorProto.ReservedRange;

                /**
                 * Creates a plain object from a ReservedRange message. Also converts values to other types if specified.
                 * @param message ReservedRange
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.DescriptorProto.ReservedRange, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this ReservedRange to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for ReservedRange
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }
        }

        /** Properties of an ExtensionRangeOptions. */
        interface IExtensionRangeOptions {

            /** ExtensionRangeOptions uninterpreted_option */
            uninterpreted_option?: (google.protobuf.IUninterpretedOption[]|null);

            /** ExtensionRangeOptions declaration */
            declaration?: (google.protobuf.ExtensionRangeOptions.IDeclaration[]|null);

            /** ExtensionRangeOptions features */
            features?: (google.protobuf.IFeatureSet|null);

            /** ExtensionRangeOptions verification */
            verification?: (google.protobuf.ExtensionRangeOptions.VerificationState|null);
        }

        /** Represents an ExtensionRangeOptions. */
        class ExtensionRangeOptions implements IExtensionRangeOptions {

            /**
             * Constructs a new ExtensionRangeOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IExtensionRangeOptions);

            /** ExtensionRangeOptions uninterpreted_option. */
            public uninterpreted_option: google.protobuf.IUninterpretedOption[];

            /** ExtensionRangeOptions declaration. */
            public declaration: google.protobuf.ExtensionRangeOptions.IDeclaration[];

            /** ExtensionRangeOptions features. */
            public features?: (google.protobuf.IFeatureSet|null);

            /** ExtensionRangeOptions verification. */
            public verification: google.protobuf.ExtensionRangeOptions.VerificationState;

            /**
             * Encodes the specified ExtensionRangeOptions message. Does not implicitly {@link google.protobuf.ExtensionRangeOptions.verify|verify} messages.
             * @param message ExtensionRangeOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IExtensionRangeOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an ExtensionRangeOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns ExtensionRangeOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.ExtensionRangeOptions;

            /**
             * Verifies an ExtensionRangeOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an ExtensionRangeOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns ExtensionRangeOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.ExtensionRangeOptions;

            /**
             * Creates a plain object from an ExtensionRangeOptions message. Also converts values to other types if specified.
             * @param message ExtensionRangeOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.ExtensionRangeOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this ExtensionRangeOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for ExtensionRangeOptions
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace ExtensionRangeOptions {

            /** Properties of a Declaration. */
            interface IDeclaration {

                /** Declaration number */
                number?: (number|null);

                /** Declaration full_name */
                full_name?: (string|null);

                /** Declaration type */
                type?: (string|null);

                /** Declaration reserved */
                reserved?: (boolean|null);

                /** Declaration repeated */
                repeated?: (boolean|null);
            }

            /** Represents a Declaration. */
            class Declaration implements IDeclaration {

                /**
                 * Constructs a new Declaration.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.ExtensionRangeOptions.IDeclaration);

                /** Declaration number. */
                public number: number;

                /** Declaration full_name. */
                public full_name: string;

                /** Declaration type. */
                public type: string;

                /** Declaration reserved. */
                public reserved: boolean;

                /** Declaration repeated. */
                public repeated: boolean;

                /**
                 * Encodes the specified Declaration message. Does not implicitly {@link google.protobuf.ExtensionRangeOptions.Declaration.verify|verify} messages.
                 * @param message Declaration message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.ExtensionRangeOptions.IDeclaration, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a Declaration message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns Declaration
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.ExtensionRangeOptions.Declaration;

                /**
                 * Verifies a Declaration message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a Declaration message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns Declaration
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.ExtensionRangeOptions.Declaration;

                /**
                 * Creates a plain object from a Declaration message. Also converts values to other types if specified.
                 * @param message Declaration
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.ExtensionRangeOptions.Declaration, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this Declaration to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for Declaration
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** VerificationState enum. */
            enum VerificationState {
                DECLARATION = 0,
                UNVERIFIED = 1
            }
        }

        /** Properties of a FieldDescriptorProto. */
        interface IFieldDescriptorProto {

            /** FieldDescriptorProto name */
            name?: (string|null);

            /** FieldDescriptorProto number */
            number?: (number|null);

            /** FieldDescriptorProto label */
            label?: (google.protobuf.FieldDescriptorProto.Label|null);

            /** FieldDescriptorProto type */
            type?: (google.protobuf.FieldDescriptorProto.Type|null);

            /** FieldDescriptorProto type_name */
            type_name?: (string|null);

            /** FieldDescriptorProto extendee */
            extendee?: (string|null);

            /** FieldDescriptorProto default_value */
            default_value?: (string|null);

            /** FieldDescriptorProto oneof_index */
            oneof_index?: (number|null);

            /** FieldDescriptorProto json_name */
            json_name?: (string|null);

            /** FieldDescriptorProto options */
            options?: (google.protobuf.IFieldOptions|null);

            /** FieldDescriptorProto proto3_optional */
            proto3_optional?: (boolean|null);
        }

        /** Represents a FieldDescriptorProto. */
        class FieldDescriptorProto implements IFieldDescriptorProto {

            /**
             * Constructs a new FieldDescriptorProto.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IFieldDescriptorProto);

            /** FieldDescriptorProto name. */
            public name: string;

            /** FieldDescriptorProto number. */
            public number: number;

            /** FieldDescriptorProto label. */
            public label: google.protobuf.FieldDescriptorProto.Label;

            /** FieldDescriptorProto type. */
            public type: google.protobuf.FieldDescriptorProto.Type;

            /** FieldDescriptorProto type_name. */
            public type_name: string;

            /** FieldDescriptorProto extendee. */
            public extendee: string;

            /** FieldDescriptorProto default_value. */
            public default_value: string;

            /** FieldDescriptorProto oneof_index. */
            public oneof_index: number;

            /** FieldDescriptorProto json_name. */
            public json_name: string;

            /** FieldDescriptorProto options. */
            public options?: (google.protobuf.IFieldOptions|null);

            /** FieldDescriptorProto proto3_optional. */
            public proto3_optional: boolean;

            /**
             * Encodes the specified FieldDescriptorProto message. Does not implicitly {@link google.protobuf.FieldDescriptorProto.verify|verify} messages.
             * @param message FieldDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IFieldDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a FieldDescriptorProto message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns FieldDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FieldDescriptorProto;

            /**
             * Verifies a FieldDescriptorProto message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a FieldDescriptorProto message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns FieldDescriptorProto
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.FieldDescriptorProto;

            /**
             * Creates a plain object from a FieldDescriptorProto message. Also converts values to other types if specified.
             * @param message FieldDescriptorProto
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.FieldDescriptorProto, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this FieldDescriptorProto to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for FieldDescriptorProto
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace FieldDescriptorProto {

            /** Type enum. */
            enum Type {
                TYPE_DOUBLE = 1,
                TYPE_FLOAT = 2,
                TYPE_INT64 = 3,
                TYPE_UINT64 = 4,
                TYPE_INT32 = 5,
                TYPE_FIXED64 = 6,
                TYPE_FIXED32 = 7,
                TYPE_BOOL = 8,
                TYPE_STRING = 9,
                TYPE_GROUP = 10,
                TYPE_MESSAGE = 11,
                TYPE_BYTES = 12,
                TYPE_UINT32 = 13,
                TYPE_ENUM = 14,
                TYPE_SFIXED32 = 15,
                TYPE_SFIXED64 = 16,
                TYPE_SINT32 = 17,
                TYPE_SINT64 = 18
            }

            /** Label enum. */
            enum Label {
                LABEL_OPTIONAL = 1,
                LABEL_REPEATED = 3,
                LABEL_REQUIRED = 2
            }
        }

        /** Properties of an OneofDescriptorProto. */
        interface IOneofDescriptorProto {

            /** OneofDescriptorProto name */
            name?: (string|null);

            /** OneofDescriptorProto options */
            options?: (google.protobuf.IOneofOptions|null);
        }

        /** Represents an OneofDescriptorProto. */
        class OneofDescriptorProto implements IOneofDescriptorProto {

            /**
             * Constructs a new OneofDescriptorProto.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IOneofDescriptorProto);

            /** OneofDescriptorProto name. */
            public name: string;

            /** OneofDescriptorProto options. */
            public options?: (google.protobuf.IOneofOptions|null);

            /**
             * Encodes the specified OneofDescriptorProto message. Does not implicitly {@link google.protobuf.OneofDescriptorProto.verify|verify} messages.
             * @param message OneofDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IOneofDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an OneofDescriptorProto message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns OneofDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.OneofDescriptorProto;

            /**
             * Verifies an OneofDescriptorProto message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an OneofDescriptorProto message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns OneofDescriptorProto
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.OneofDescriptorProto;

            /**
             * Creates a plain object from an OneofDescriptorProto message. Also converts values to other types if specified.
             * @param message OneofDescriptorProto
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.OneofDescriptorProto, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this OneofDescriptorProto to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for OneofDescriptorProto
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of an EnumDescriptorProto. */
        interface IEnumDescriptorProto {

            /** EnumDescriptorProto name */
            name?: (string|null);

            /** EnumDescriptorProto value */
            value?: (google.protobuf.IEnumValueDescriptorProto[]|null);

            /** EnumDescriptorProto options */
            options?: (google.protobuf.IEnumOptions|null);

            /** EnumDescriptorProto reserved_range */
            reserved_range?: (google.protobuf.EnumDescriptorProto.IEnumReservedRange[]|null);

            /** EnumDescriptorProto reserved_name */
            reserved_name?: (string[]|null);

            /** EnumDescriptorProto visibility */
            visibility?: (google.protobuf.SymbolVisibility|null);
        }

        /** Represents an EnumDescriptorProto. */
        class EnumDescriptorProto implements IEnumDescriptorProto {

            /**
             * Constructs a new EnumDescriptorProto.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IEnumDescriptorProto);

            /** EnumDescriptorProto name. */
            public name: string;

            /** EnumDescriptorProto value. */
            public value: google.protobuf.IEnumValueDescriptorProto[];

            /** EnumDescriptorProto options. */
            public options?: (google.protobuf.IEnumOptions|null);

            /** EnumDescriptorProto reserved_range. */
            public reserved_range: google.protobuf.EnumDescriptorProto.IEnumReservedRange[];

            /** EnumDescriptorProto reserved_name. */
            public reserved_name: string[];

            /** EnumDescriptorProto visibility. */
            public visibility: google.protobuf.SymbolVisibility;

            /**
             * Encodes the specified EnumDescriptorProto message. Does not implicitly {@link google.protobuf.EnumDescriptorProto.verify|verify} messages.
             * @param message EnumDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IEnumDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an EnumDescriptorProto message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns EnumDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.EnumDescriptorProto;

            /**
             * Verifies an EnumDescriptorProto message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an EnumDescriptorProto message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns EnumDescriptorProto
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.EnumDescriptorProto;

            /**
             * Creates a plain object from an EnumDescriptorProto message. Also converts values to other types if specified.
             * @param message EnumDescriptorProto
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.EnumDescriptorProto, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this EnumDescriptorProto to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for EnumDescriptorProto
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace EnumDescriptorProto {

            /** Properties of an EnumReservedRange. */
            interface IEnumReservedRange {

                /** EnumReservedRange start */
                start?: (number|null);

                /** EnumReservedRange end */
                end?: (number|null);
            }

            /** Represents an EnumReservedRange. */
            class EnumReservedRange implements IEnumReservedRange {

                /**
                 * Constructs a new EnumReservedRange.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.EnumDescriptorProto.IEnumReservedRange);

                /** EnumReservedRange start. */
                public start: number;

                /** EnumReservedRange end. */
                public end: number;

                /**
                 * Encodes the specified EnumReservedRange message. Does not implicitly {@link google.protobuf.EnumDescriptorProto.EnumReservedRange.verify|verify} messages.
                 * @param message EnumReservedRange message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.EnumDescriptorProto.IEnumReservedRange, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes an EnumReservedRange message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns EnumReservedRange
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.EnumDescriptorProto.EnumReservedRange;

                /**
                 * Verifies an EnumReservedRange message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates an EnumReservedRange message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns EnumReservedRange
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.EnumDescriptorProto.EnumReservedRange;

                /**
                 * Creates a plain object from an EnumReservedRange message. Also converts values to other types if specified.
                 * @param message EnumReservedRange
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.EnumDescriptorProto.EnumReservedRange, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this EnumReservedRange to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for EnumReservedRange
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }
        }

        /** Properties of an EnumValueDescriptorProto. */
        interface IEnumValueDescriptorProto {

            /** EnumValueDescriptorProto name */
            name?: (string|null);

            /** EnumValueDescriptorProto number */
            number?: (number|null);

            /** EnumValueDescriptorProto options */
            options?: (google.protobuf.IEnumValueOptions|null);
        }

        /** Represents an EnumValueDescriptorProto. */
        class EnumValueDescriptorProto implements IEnumValueDescriptorProto {

            /**
             * Constructs a new EnumValueDescriptorProto.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IEnumValueDescriptorProto);

            /** EnumValueDescriptorProto name. */
            public name: string;

            /** EnumValueDescriptorProto number. */
            public number: number;

            /** EnumValueDescriptorProto options. */
            public options?: (google.protobuf.IEnumValueOptions|null);

            /**
             * Encodes the specified EnumValueDescriptorProto message. Does not implicitly {@link google.protobuf.EnumValueDescriptorProto.verify|verify} messages.
             * @param message EnumValueDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IEnumValueDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an EnumValueDescriptorProto message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns EnumValueDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.EnumValueDescriptorProto;

            /**
             * Verifies an EnumValueDescriptorProto message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an EnumValueDescriptorProto message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns EnumValueDescriptorProto
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.EnumValueDescriptorProto;

            /**
             * Creates a plain object from an EnumValueDescriptorProto message. Also converts values to other types if specified.
             * @param message EnumValueDescriptorProto
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.EnumValueDescriptorProto, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this EnumValueDescriptorProto to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for EnumValueDescriptorProto
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a ServiceDescriptorProto. */
        interface IServiceDescriptorProto {

            /** ServiceDescriptorProto name */
            name?: (string|null);

            /** ServiceDescriptorProto method */
            method?: (google.protobuf.IMethodDescriptorProto[]|null);

            /** ServiceDescriptorProto options */
            options?: (google.protobuf.IServiceOptions|null);
        }

        /** Represents a ServiceDescriptorProto. */
        class ServiceDescriptorProto implements IServiceDescriptorProto {

            /**
             * Constructs a new ServiceDescriptorProto.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IServiceDescriptorProto);

            /** ServiceDescriptorProto name. */
            public name: string;

            /** ServiceDescriptorProto method. */
            public method: google.protobuf.IMethodDescriptorProto[];

            /** ServiceDescriptorProto options. */
            public options?: (google.protobuf.IServiceOptions|null);

            /**
             * Encodes the specified ServiceDescriptorProto message. Does not implicitly {@link google.protobuf.ServiceDescriptorProto.verify|verify} messages.
             * @param message ServiceDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IServiceDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a ServiceDescriptorProto message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns ServiceDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.ServiceDescriptorProto;

            /**
             * Verifies a ServiceDescriptorProto message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a ServiceDescriptorProto message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns ServiceDescriptorProto
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.ServiceDescriptorProto;

            /**
             * Creates a plain object from a ServiceDescriptorProto message. Also converts values to other types if specified.
             * @param message ServiceDescriptorProto
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.ServiceDescriptorProto, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this ServiceDescriptorProto to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for ServiceDescriptorProto
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a MethodDescriptorProto. */
        interface IMethodDescriptorProto {

            /** MethodDescriptorProto name */
            name?: (string|null);

            /** MethodDescriptorProto input_type */
            input_type?: (string|null);

            /** MethodDescriptorProto output_type */
            output_type?: (string|null);

            /** MethodDescriptorProto options */
            options?: (google.protobuf.IMethodOptions|null);

            /** MethodDescriptorProto client_streaming */
            client_streaming?: (boolean|null);

            /** MethodDescriptorProto server_streaming */
            server_streaming?: (boolean|null);
        }

        /** Represents a MethodDescriptorProto. */
        class MethodDescriptorProto implements IMethodDescriptorProto {

            /**
             * Constructs a new MethodDescriptorProto.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IMethodDescriptorProto);

            /** MethodDescriptorProto name. */
            public name: string;

            /** MethodDescriptorProto input_type. */
            public input_type: string;

            /** MethodDescriptorProto output_type. */
            public output_type: string;

            /** MethodDescriptorProto options. */
            public options?: (google.protobuf.IMethodOptions|null);

            /** MethodDescriptorProto client_streaming. */
            public client_streaming: boolean;

            /** MethodDescriptorProto server_streaming. */
            public server_streaming: boolean;

            /**
             * Encodes the specified MethodDescriptorProto message. Does not implicitly {@link google.protobuf.MethodDescriptorProto.verify|verify} messages.
             * @param message MethodDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IMethodDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a MethodDescriptorProto message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns MethodDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.MethodDescriptorProto;

            /**
             * Verifies a MethodDescriptorProto message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a MethodDescriptorProto message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns MethodDescriptorProto
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.MethodDescriptorProto;

            /**
             * Creates a plain object from a MethodDescriptorProto message. Also converts values to other types if specified.
             * @param message MethodDescriptorProto
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.MethodDescriptorProto, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this MethodDescriptorProto to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for MethodDescriptorProto
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a FileOptions. */
        interface IFileOptions {

            /** FileOptions java_package */
            java_package?: (string|null);

            /** FileOptions java_outer_classname */
            java_outer_classname?: (string|null);

            /** FileOptions java_multiple_files */
            java_multiple_files?: (boolean|null);

            /** FileOptions java_generate_equals_and_hash */
            java_generate_equals_and_hash?: (boolean|null);

            /** FileOptions java_string_check_utf8 */
            java_string_check_utf8?: (boolean|null);

            /** FileOptions optimize_for */
            optimize_for?: (google.protobuf.FileOptions.OptimizeMode|null);

            /** FileOptions go_package */
            go_package?: (string|null);

            /** FileOptions cc_generic_services */
            cc_generic_services?: (boolean|null);

            /** FileOptions java_generic_services */
            java_generic_services?: (boolean|null);

            /** FileOptions py_generic_services */
            py_generic_services?: (boolean|null);

            /** FileOptions deprecated */
            deprecated?: (boolean|null);

            /** FileOptions cc_enable_arenas */
            cc_enable_arenas?: (boolean|null);

            /** FileOptions objc_class_prefix */
            objc_class_prefix?: (string|null);

            /** FileOptions csharp_namespace */
            csharp_namespace?: (string|null);

            /** FileOptions swift_prefix */
            swift_prefix?: (string|null);

            /** FileOptions php_class_prefix */
            php_class_prefix?: (string|null);

            /** FileOptions php_namespace */
            php_namespace?: (string|null);

            /** FileOptions php_metadata_namespace */
            php_metadata_namespace?: (string|null);

            /** FileOptions ruby_package */
            ruby_package?: (string|null);

            /** FileOptions features */
            features?: (google.protobuf.IFeatureSet|null);

            /** FileOptions uninterpreted_option */
            uninterpreted_option?: (google.protobuf.IUninterpretedOption[]|null);
        }

        /** Represents a FileOptions. */
        class FileOptions implements IFileOptions {

            /**
             * Constructs a new FileOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IFileOptions);

            /** FileOptions java_package. */
            public java_package: string;

            /** FileOptions java_outer_classname. */
            public java_outer_classname: string;

            /** FileOptions java_multiple_files. */
            public java_multiple_files: boolean;

            /** FileOptions java_generate_equals_and_hash. */
            public java_generate_equals_and_hash: boolean;

            /** FileOptions java_string_check_utf8. */
            public java_string_check_utf8: boolean;

            /** FileOptions optimize_for. */
            public optimize_for: google.protobuf.FileOptions.OptimizeMode;

            /** FileOptions go_package. */
            public go_package: string;

            /** FileOptions cc_generic_services. */
            public cc_generic_services: boolean;

            /** FileOptions java_generic_services. */
            public java_generic_services: boolean;

            /** FileOptions py_generic_services. */
            public py_generic_services: boolean;

            /** FileOptions deprecated. */
            public deprecated: boolean;

            /** FileOptions cc_enable_arenas. */
            public cc_enable_arenas: boolean;

            /** FileOptions objc_class_prefix. */
            public objc_class_prefix: string;

            /** FileOptions csharp_namespace. */
            public csharp_namespace: string;

            /** FileOptions swift_prefix. */
            public swift_prefix: string;

            /** FileOptions php_class_prefix. */
            public php_class_prefix: string;

            /** FileOptions php_namespace. */
            public php_namespace: string;

            /** FileOptions php_metadata_namespace. */
            public php_metadata_namespace: string;

            /** FileOptions ruby_package. */
            public ruby_package: string;

            /** FileOptions features. */
            public features?: (google.protobuf.IFeatureSet|null);

            /** FileOptions uninterpreted_option. */
            public uninterpreted_option: google.protobuf.IUninterpretedOption[];

            /**
             * Encodes the specified FileOptions message. Does not implicitly {@link google.protobuf.FileOptions.verify|verify} messages.
             * @param message FileOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IFileOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a FileOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns FileOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FileOptions;

            /**
             * Verifies a FileOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a FileOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns FileOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.FileOptions;

            /**
             * Creates a plain object from a FileOptions message. Also converts values to other types if specified.
             * @param message FileOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.FileOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this FileOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for FileOptions
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace FileOptions {

            /** OptimizeMode enum. */
            enum OptimizeMode {
                SPEED = 1,
                CODE_SIZE = 2,
                LITE_RUNTIME = 3
            }
        }

        /** Properties of a MessageOptions. */
        interface IMessageOptions {

            /** MessageOptions message_set_wire_format */
            message_set_wire_format?: (boolean|null);

            /** MessageOptions no_standard_descriptor_accessor */
            no_standard_descriptor_accessor?: (boolean|null);

            /** MessageOptions deprecated */
            deprecated?: (boolean|null);

            /** MessageOptions map_entry */
            map_entry?: (boolean|null);

            /** MessageOptions deprecated_legacy_json_field_conflicts */
            deprecated_legacy_json_field_conflicts?: (boolean|null);

            /** MessageOptions features */
            features?: (google.protobuf.IFeatureSet|null);

            /** MessageOptions uninterpreted_option */
            uninterpreted_option?: (google.protobuf.IUninterpretedOption[]|null);

            /** MessageOptions .validate.disabled */
            ".validate.disabled"?: (boolean|null);

            /** MessageOptions .validate.ignored */
            ".validate.ignored"?: (boolean|null);
        }

        /** Represents a MessageOptions. */
        class MessageOptions implements IMessageOptions {

            /**
             * Constructs a new MessageOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IMessageOptions);

            /** MessageOptions message_set_wire_format. */
            public message_set_wire_format: boolean;

            /** MessageOptions no_standard_descriptor_accessor. */
            public no_standard_descriptor_accessor: boolean;

            /** MessageOptions deprecated. */
            public deprecated: boolean;

            /** MessageOptions map_entry. */
            public map_entry: boolean;

            /** MessageOptions deprecated_legacy_json_field_conflicts. */
            public deprecated_legacy_json_field_conflicts: boolean;

            /** MessageOptions features. */
            public features?: (google.protobuf.IFeatureSet|null);

            /** MessageOptions uninterpreted_option. */
            public uninterpreted_option: google.protobuf.IUninterpretedOption[];

            /**
             * Encodes the specified MessageOptions message. Does not implicitly {@link google.protobuf.MessageOptions.verify|verify} messages.
             * @param message MessageOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IMessageOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a MessageOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns MessageOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.MessageOptions;

            /**
             * Verifies a MessageOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a MessageOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns MessageOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.MessageOptions;

            /**
             * Creates a plain object from a MessageOptions message. Also converts values to other types if specified.
             * @param message MessageOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.MessageOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this MessageOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for MessageOptions
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a FieldOptions. */
        interface IFieldOptions {

            /** FieldOptions ctype */
            ctype?: (google.protobuf.FieldOptions.CType|null);

            /** FieldOptions packed */
            packed?: (boolean|null);

            /** FieldOptions jstype */
            jstype?: (google.protobuf.FieldOptions.JSType|null);

            /** FieldOptions lazy */
            lazy?: (boolean|null);

            /** FieldOptions unverified_lazy */
            unverified_lazy?: (boolean|null);

            /** FieldOptions deprecated */
            deprecated?: (boolean|null);

            /** FieldOptions weak */
            weak?: (boolean|null);

            /** FieldOptions debug_redact */
            debug_redact?: (boolean|null);

            /** FieldOptions retention */
            retention?: (google.protobuf.FieldOptions.OptionRetention|null);

            /** FieldOptions targets */
            targets?: (google.protobuf.FieldOptions.OptionTargetType[]|null);

            /** FieldOptions edition_defaults */
            edition_defaults?: (google.protobuf.FieldOptions.IEditionDefault[]|null);

            /** FieldOptions features */
            features?: (google.protobuf.IFeatureSet|null);

            /** FieldOptions feature_support */
            feature_support?: (google.protobuf.FieldOptions.IFeatureSupport|null);

            /** FieldOptions uninterpreted_option */
            uninterpreted_option?: (google.protobuf.IUninterpretedOption[]|null);

            /** FieldOptions .validate.rules */
            ".validate.rules"?: (validate.IFieldRules|null);
        }

        /** Represents a FieldOptions. */
        class FieldOptions implements IFieldOptions {

            /**
             * Constructs a new FieldOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IFieldOptions);

            /** FieldOptions ctype. */
            public ctype: google.protobuf.FieldOptions.CType;

            /** FieldOptions packed. */
            public packed: boolean;

            /** FieldOptions jstype. */
            public jstype: google.protobuf.FieldOptions.JSType;

            /** FieldOptions lazy. */
            public lazy: boolean;

            /** FieldOptions unverified_lazy. */
            public unverified_lazy: boolean;

            /** FieldOptions deprecated. */
            public deprecated: boolean;

            /** FieldOptions weak. */
            public weak: boolean;

            /** FieldOptions debug_redact. */
            public debug_redact: boolean;

            /** FieldOptions retention. */
            public retention: google.protobuf.FieldOptions.OptionRetention;

            /** FieldOptions targets. */
            public targets: google.protobuf.FieldOptions.OptionTargetType[];

            /** FieldOptions edition_defaults. */
            public edition_defaults: google.protobuf.FieldOptions.IEditionDefault[];

            /** FieldOptions features. */
            public features?: (google.protobuf.IFeatureSet|null);

            /** FieldOptions feature_support. */
            public feature_support?: (google.protobuf.FieldOptions.IFeatureSupport|null);

            /** FieldOptions uninterpreted_option. */
            public uninterpreted_option: google.protobuf.IUninterpretedOption[];

            /**
             * Encodes the specified FieldOptions message. Does not implicitly {@link google.protobuf.FieldOptions.verify|verify} messages.
             * @param message FieldOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IFieldOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a FieldOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns FieldOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FieldOptions;

            /**
             * Verifies a FieldOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a FieldOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns FieldOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.FieldOptions;

            /**
             * Creates a plain object from a FieldOptions message. Also converts values to other types if specified.
             * @param message FieldOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.FieldOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this FieldOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for FieldOptions
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace FieldOptions {

            /** CType enum. */
            enum CType {
                STRING = 0,
                CORD = 1,
                STRING_PIECE = 2
            }

            /** JSType enum. */
            enum JSType {
                JS_NORMAL = 0,
                JS_STRING = 1,
                JS_NUMBER = 2
            }

            /** OptionRetention enum. */
            enum OptionRetention {
                RETENTION_UNKNOWN = 0,
                RETENTION_RUNTIME = 1,
                RETENTION_SOURCE = 2
            }

            /** OptionTargetType enum. */
            enum OptionTargetType {
                TARGET_TYPE_UNKNOWN = 0,
                TARGET_TYPE_FILE = 1,
                TARGET_TYPE_EXTENSION_RANGE = 2,
                TARGET_TYPE_MESSAGE = 3,
                TARGET_TYPE_FIELD = 4,
                TARGET_TYPE_ONEOF = 5,
                TARGET_TYPE_ENUM = 6,
                TARGET_TYPE_ENUM_ENTRY = 7,
                TARGET_TYPE_SERVICE = 8,
                TARGET_TYPE_METHOD = 9
            }

            /** Properties of an EditionDefault. */
            interface IEditionDefault {

                /** EditionDefault edition */
                edition?: (google.protobuf.Edition|null);

                /** EditionDefault value */
                value?: (string|null);
            }

            /** Represents an EditionDefault. */
            class EditionDefault implements IEditionDefault {

                /**
                 * Constructs a new EditionDefault.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.FieldOptions.IEditionDefault);

                /** EditionDefault edition. */
                public edition: google.protobuf.Edition;

                /** EditionDefault value. */
                public value: string;

                /**
                 * Encodes the specified EditionDefault message. Does not implicitly {@link google.protobuf.FieldOptions.EditionDefault.verify|verify} messages.
                 * @param message EditionDefault message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.FieldOptions.IEditionDefault, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes an EditionDefault message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns EditionDefault
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FieldOptions.EditionDefault;

                /**
                 * Verifies an EditionDefault message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates an EditionDefault message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns EditionDefault
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.FieldOptions.EditionDefault;

                /**
                 * Creates a plain object from an EditionDefault message. Also converts values to other types if specified.
                 * @param message EditionDefault
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.FieldOptions.EditionDefault, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this EditionDefault to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for EditionDefault
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a FeatureSupport. */
            interface IFeatureSupport {

                /** FeatureSupport edition_introduced */
                edition_introduced?: (google.protobuf.Edition|null);

                /** FeatureSupport edition_deprecated */
                edition_deprecated?: (google.protobuf.Edition|null);

                /** FeatureSupport deprecation_warning */
                deprecation_warning?: (string|null);

                /** FeatureSupport edition_removed */
                edition_removed?: (google.protobuf.Edition|null);
            }

            /** Represents a FeatureSupport. */
            class FeatureSupport implements IFeatureSupport {

                /**
                 * Constructs a new FeatureSupport.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.FieldOptions.IFeatureSupport);

                /** FeatureSupport edition_introduced. */
                public edition_introduced: google.protobuf.Edition;

                /** FeatureSupport edition_deprecated. */
                public edition_deprecated: google.protobuf.Edition;

                /** FeatureSupport deprecation_warning. */
                public deprecation_warning: string;

                /** FeatureSupport edition_removed. */
                public edition_removed: google.protobuf.Edition;

                /**
                 * Encodes the specified FeatureSupport message. Does not implicitly {@link google.protobuf.FieldOptions.FeatureSupport.verify|verify} messages.
                 * @param message FeatureSupport message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.FieldOptions.IFeatureSupport, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a FeatureSupport message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns FeatureSupport
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FieldOptions.FeatureSupport;

                /**
                 * Verifies a FeatureSupport message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a FeatureSupport message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns FeatureSupport
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.FieldOptions.FeatureSupport;

                /**
                 * Creates a plain object from a FeatureSupport message. Also converts values to other types if specified.
                 * @param message FeatureSupport
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.FieldOptions.FeatureSupport, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this FeatureSupport to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for FeatureSupport
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }
        }

        /** Properties of an OneofOptions. */
        interface IOneofOptions {

            /** OneofOptions features */
            features?: (google.protobuf.IFeatureSet|null);

            /** OneofOptions uninterpreted_option */
            uninterpreted_option?: (google.protobuf.IUninterpretedOption[]|null);

            /** OneofOptions .validate.required */
            ".validate.required"?: (boolean|null);
        }

        /** Represents an OneofOptions. */
        class OneofOptions implements IOneofOptions {

            /**
             * Constructs a new OneofOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IOneofOptions);

            /** OneofOptions features. */
            public features?: (google.protobuf.IFeatureSet|null);

            /** OneofOptions uninterpreted_option. */
            public uninterpreted_option: google.protobuf.IUninterpretedOption[];

            /**
             * Encodes the specified OneofOptions message. Does not implicitly {@link google.protobuf.OneofOptions.verify|verify} messages.
             * @param message OneofOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IOneofOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an OneofOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns OneofOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.OneofOptions;

            /**
             * Verifies an OneofOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an OneofOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns OneofOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.OneofOptions;

            /**
             * Creates a plain object from an OneofOptions message. Also converts values to other types if specified.
             * @param message OneofOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.OneofOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this OneofOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for OneofOptions
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of an EnumOptions. */
        interface IEnumOptions {

            /** EnumOptions allow_alias */
            allow_alias?: (boolean|null);

            /** EnumOptions deprecated */
            deprecated?: (boolean|null);

            /** EnumOptions deprecated_legacy_json_field_conflicts */
            deprecated_legacy_json_field_conflicts?: (boolean|null);

            /** EnumOptions features */
            features?: (google.protobuf.IFeatureSet|null);

            /** EnumOptions uninterpreted_option */
            uninterpreted_option?: (google.protobuf.IUninterpretedOption[]|null);
        }

        /** Represents an EnumOptions. */
        class EnumOptions implements IEnumOptions {

            /**
             * Constructs a new EnumOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IEnumOptions);

            /** EnumOptions allow_alias. */
            public allow_alias: boolean;

            /** EnumOptions deprecated. */
            public deprecated: boolean;

            /** EnumOptions deprecated_legacy_json_field_conflicts. */
            public deprecated_legacy_json_field_conflicts: boolean;

            /** EnumOptions features. */
            public features?: (google.protobuf.IFeatureSet|null);

            /** EnumOptions uninterpreted_option. */
            public uninterpreted_option: google.protobuf.IUninterpretedOption[];

            /**
             * Encodes the specified EnumOptions message. Does not implicitly {@link google.protobuf.EnumOptions.verify|verify} messages.
             * @param message EnumOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IEnumOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an EnumOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns EnumOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.EnumOptions;

            /**
             * Verifies an EnumOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an EnumOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns EnumOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.EnumOptions;

            /**
             * Creates a plain object from an EnumOptions message. Also converts values to other types if specified.
             * @param message EnumOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.EnumOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this EnumOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for EnumOptions
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of an EnumValueOptions. */
        interface IEnumValueOptions {

            /** EnumValueOptions deprecated */
            deprecated?: (boolean|null);

            /** EnumValueOptions features */
            features?: (google.protobuf.IFeatureSet|null);

            /** EnumValueOptions debug_redact */
            debug_redact?: (boolean|null);

            /** EnumValueOptions feature_support */
            feature_support?: (google.protobuf.FieldOptions.IFeatureSupport|null);

            /** EnumValueOptions uninterpreted_option */
            uninterpreted_option?: (google.protobuf.IUninterpretedOption[]|null);
        }

        /** Represents an EnumValueOptions. */
        class EnumValueOptions implements IEnumValueOptions {

            /**
             * Constructs a new EnumValueOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IEnumValueOptions);

            /** EnumValueOptions deprecated. */
            public deprecated: boolean;

            /** EnumValueOptions features. */
            public features?: (google.protobuf.IFeatureSet|null);

            /** EnumValueOptions debug_redact. */
            public debug_redact: boolean;

            /** EnumValueOptions feature_support. */
            public feature_support?: (google.protobuf.FieldOptions.IFeatureSupport|null);

            /** EnumValueOptions uninterpreted_option. */
            public uninterpreted_option: google.protobuf.IUninterpretedOption[];

            /**
             * Encodes the specified EnumValueOptions message. Does not implicitly {@link google.protobuf.EnumValueOptions.verify|verify} messages.
             * @param message EnumValueOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IEnumValueOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an EnumValueOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns EnumValueOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.EnumValueOptions;

            /**
             * Verifies an EnumValueOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an EnumValueOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns EnumValueOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.EnumValueOptions;

            /**
             * Creates a plain object from an EnumValueOptions message. Also converts values to other types if specified.
             * @param message EnumValueOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.EnumValueOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this EnumValueOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for EnumValueOptions
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a ServiceOptions. */
        interface IServiceOptions {

            /** ServiceOptions features */
            features?: (google.protobuf.IFeatureSet|null);

            /** ServiceOptions deprecated */
            deprecated?: (boolean|null);

            /** ServiceOptions uninterpreted_option */
            uninterpreted_option?: (google.protobuf.IUninterpretedOption[]|null);
        }

        /** Represents a ServiceOptions. */
        class ServiceOptions implements IServiceOptions {

            /**
             * Constructs a new ServiceOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IServiceOptions);

            /** ServiceOptions features. */
            public features?: (google.protobuf.IFeatureSet|null);

            /** ServiceOptions deprecated. */
            public deprecated: boolean;

            /** ServiceOptions uninterpreted_option. */
            public uninterpreted_option: google.protobuf.IUninterpretedOption[];

            /**
             * Encodes the specified ServiceOptions message. Does not implicitly {@link google.protobuf.ServiceOptions.verify|verify} messages.
             * @param message ServiceOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IServiceOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a ServiceOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns ServiceOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.ServiceOptions;

            /**
             * Verifies a ServiceOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a ServiceOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns ServiceOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.ServiceOptions;

            /**
             * Creates a plain object from a ServiceOptions message. Also converts values to other types if specified.
             * @param message ServiceOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.ServiceOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this ServiceOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for ServiceOptions
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a MethodOptions. */
        interface IMethodOptions {

            /** MethodOptions deprecated */
            deprecated?: (boolean|null);

            /** MethodOptions idempotency_level */
            idempotency_level?: (google.protobuf.MethodOptions.IdempotencyLevel|null);

            /** MethodOptions features */
            features?: (google.protobuf.IFeatureSet|null);

            /** MethodOptions uninterpreted_option */
            uninterpreted_option?: (google.protobuf.IUninterpretedOption[]|null);
        }

        /** Represents a MethodOptions. */
        class MethodOptions implements IMethodOptions {

            /**
             * Constructs a new MethodOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IMethodOptions);

            /** MethodOptions deprecated. */
            public deprecated: boolean;

            /** MethodOptions idempotency_level. */
            public idempotency_level: google.protobuf.MethodOptions.IdempotencyLevel;

            /** MethodOptions features. */
            public features?: (google.protobuf.IFeatureSet|null);

            /** MethodOptions uninterpreted_option. */
            public uninterpreted_option: google.protobuf.IUninterpretedOption[];

            /**
             * Encodes the specified MethodOptions message. Does not implicitly {@link google.protobuf.MethodOptions.verify|verify} messages.
             * @param message MethodOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IMethodOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a MethodOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns MethodOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.MethodOptions;

            /**
             * Verifies a MethodOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a MethodOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns MethodOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.MethodOptions;

            /**
             * Creates a plain object from a MethodOptions message. Also converts values to other types if specified.
             * @param message MethodOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.MethodOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this MethodOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for MethodOptions
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace MethodOptions {

            /** IdempotencyLevel enum. */
            enum IdempotencyLevel {
                IDEMPOTENCY_UNKNOWN = 0,
                NO_SIDE_EFFECTS = 1,
                IDEMPOTENT = 2
            }
        }

        /** Properties of an UninterpretedOption. */
        interface IUninterpretedOption {

            /** UninterpretedOption name */
            name?: (google.protobuf.UninterpretedOption.INamePart[]|null);

            /** UninterpretedOption identifier_value */
            identifier_value?: (string|null);

            /** UninterpretedOption positive_int_value */
            positive_int_value?: (number|Long|null);

            /** UninterpretedOption negative_int_value */
            negative_int_value?: (number|Long|null);

            /** UninterpretedOption double_value */
            double_value?: (number|null);

            /** UninterpretedOption string_value */
            string_value?: (Uint8Array|null);

            /** UninterpretedOption aggregate_value */
            aggregate_value?: (string|null);
        }

        /** Represents an UninterpretedOption. */
        class UninterpretedOption implements IUninterpretedOption {

            /**
             * Constructs a new UninterpretedOption.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IUninterpretedOption);

            /** UninterpretedOption name. */
            public name: google.protobuf.UninterpretedOption.INamePart[];

            /** UninterpretedOption identifier_value. */
            public identifier_value: string;

            /** UninterpretedOption positive_int_value. */
            public positive_int_value: (number|Long);

            /** UninterpretedOption negative_int_value. */
            public negative_int_value: (number|Long);

            /** UninterpretedOption double_value. */
            public double_value: number;

            /** UninterpretedOption string_value. */
            public string_value: Uint8Array;

            /** UninterpretedOption aggregate_value. */
            public aggregate_value: string;

            /**
             * Encodes the specified UninterpretedOption message. Does not implicitly {@link google.protobuf.UninterpretedOption.verify|verify} messages.
             * @param message UninterpretedOption message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IUninterpretedOption, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an UninterpretedOption message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns UninterpretedOption
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.UninterpretedOption;

            /**
             * Verifies an UninterpretedOption message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an UninterpretedOption message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns UninterpretedOption
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.UninterpretedOption;

            /**
             * Creates a plain object from an UninterpretedOption message. Also converts values to other types if specified.
             * @param message UninterpretedOption
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.UninterpretedOption, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this UninterpretedOption to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for UninterpretedOption
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace UninterpretedOption {

            /** Properties of a NamePart. */
            interface INamePart {

                /** NamePart name_part */
                name_part: string;

                /** NamePart is_extension */
                is_extension: boolean;
            }

            /** Represents a NamePart. */
            class NamePart implements INamePart {

                /**
                 * Constructs a new NamePart.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.UninterpretedOption.INamePart);

                /** NamePart name_part. */
                public name_part: string;

                /** NamePart is_extension. */
                public is_extension: boolean;

                /**
                 * Encodes the specified NamePart message. Does not implicitly {@link google.protobuf.UninterpretedOption.NamePart.verify|verify} messages.
                 * @param message NamePart message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.UninterpretedOption.INamePart, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a NamePart message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns NamePart
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.UninterpretedOption.NamePart;

                /**
                 * Verifies a NamePart message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a NamePart message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns NamePart
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.UninterpretedOption.NamePart;

                /**
                 * Creates a plain object from a NamePart message. Also converts values to other types if specified.
                 * @param message NamePart
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.UninterpretedOption.NamePart, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this NamePart to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for NamePart
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }
        }

        /** Properties of a FeatureSet. */
        interface IFeatureSet {

            /** FeatureSet field_presence */
            field_presence?: (google.protobuf.FeatureSet.FieldPresence|null);

            /** FeatureSet enum_type */
            enum_type?: (google.protobuf.FeatureSet.EnumType|null);

            /** FeatureSet repeated_field_encoding */
            repeated_field_encoding?: (google.protobuf.FeatureSet.RepeatedFieldEncoding|null);

            /** FeatureSet utf8_validation */
            utf8_validation?: (google.protobuf.FeatureSet.Utf8Validation|null);

            /** FeatureSet message_encoding */
            message_encoding?: (google.protobuf.FeatureSet.MessageEncoding|null);

            /** FeatureSet json_format */
            json_format?: (google.protobuf.FeatureSet.JsonFormat|null);

            /** FeatureSet enforce_naming_style */
            enforce_naming_style?: (google.protobuf.FeatureSet.EnforceNamingStyle|null);

            /** FeatureSet default_symbol_visibility */
            default_symbol_visibility?: (google.protobuf.FeatureSet.VisibilityFeature.DefaultSymbolVisibility|null);
        }

        /** Represents a FeatureSet. */
        class FeatureSet implements IFeatureSet {

            /**
             * Constructs a new FeatureSet.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IFeatureSet);

            /** FeatureSet field_presence. */
            public field_presence: google.protobuf.FeatureSet.FieldPresence;

            /** FeatureSet enum_type. */
            public enum_type: google.protobuf.FeatureSet.EnumType;

            /** FeatureSet repeated_field_encoding. */
            public repeated_field_encoding: google.protobuf.FeatureSet.RepeatedFieldEncoding;

            /** FeatureSet utf8_validation. */
            public utf8_validation: google.protobuf.FeatureSet.Utf8Validation;

            /** FeatureSet message_encoding. */
            public message_encoding: google.protobuf.FeatureSet.MessageEncoding;

            /** FeatureSet json_format. */
            public json_format: google.protobuf.FeatureSet.JsonFormat;

            /** FeatureSet enforce_naming_style. */
            public enforce_naming_style: google.protobuf.FeatureSet.EnforceNamingStyle;

            /** FeatureSet default_symbol_visibility. */
            public default_symbol_visibility: google.protobuf.FeatureSet.VisibilityFeature.DefaultSymbolVisibility;

            /**
             * Encodes the specified FeatureSet message. Does not implicitly {@link google.protobuf.FeatureSet.verify|verify} messages.
             * @param message FeatureSet message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IFeatureSet, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a FeatureSet message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns FeatureSet
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FeatureSet;

            /**
             * Verifies a FeatureSet message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a FeatureSet message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns FeatureSet
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.FeatureSet;

            /**
             * Creates a plain object from a FeatureSet message. Also converts values to other types if specified.
             * @param message FeatureSet
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.FeatureSet, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this FeatureSet to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for FeatureSet
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace FeatureSet {

            /** FieldPresence enum. */
            enum FieldPresence {
                FIELD_PRESENCE_UNKNOWN = 0,
                EXPLICIT = 1,
                IMPLICIT = 2,
                LEGACY_REQUIRED = 3
            }

            /** EnumType enum. */
            enum EnumType {
                ENUM_TYPE_UNKNOWN = 0,
                OPEN = 1,
                CLOSED = 2
            }

            /** RepeatedFieldEncoding enum. */
            enum RepeatedFieldEncoding {
                REPEATED_FIELD_ENCODING_UNKNOWN = 0,
                PACKED = 1,
                EXPANDED = 2
            }

            /** Utf8Validation enum. */
            enum Utf8Validation {
                UTF8_VALIDATION_UNKNOWN = 0,
                VERIFY = 2,
                NONE = 3
            }

            /** MessageEncoding enum. */
            enum MessageEncoding {
                MESSAGE_ENCODING_UNKNOWN = 0,
                LENGTH_PREFIXED = 1,
                DELIMITED = 2
            }

            /** JsonFormat enum. */
            enum JsonFormat {
                JSON_FORMAT_UNKNOWN = 0,
                ALLOW = 1,
                LEGACY_BEST_EFFORT = 2
            }

            /** EnforceNamingStyle enum. */
            enum EnforceNamingStyle {
                ENFORCE_NAMING_STYLE_UNKNOWN = 0,
                STYLE2024 = 1,
                STYLE_LEGACY = 2
            }

            /** Properties of a VisibilityFeature. */
            interface IVisibilityFeature {
            }

            /** Represents a VisibilityFeature. */
            class VisibilityFeature implements IVisibilityFeature {

                /**
                 * Constructs a new VisibilityFeature.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.FeatureSet.IVisibilityFeature);

                /**
                 * Encodes the specified VisibilityFeature message. Does not implicitly {@link google.protobuf.FeatureSet.VisibilityFeature.verify|verify} messages.
                 * @param message VisibilityFeature message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.FeatureSet.IVisibilityFeature, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a VisibilityFeature message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns VisibilityFeature
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FeatureSet.VisibilityFeature;

                /**
                 * Verifies a VisibilityFeature message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a VisibilityFeature message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns VisibilityFeature
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.FeatureSet.VisibilityFeature;

                /**
                 * Creates a plain object from a VisibilityFeature message. Also converts values to other types if specified.
                 * @param message VisibilityFeature
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.FeatureSet.VisibilityFeature, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this VisibilityFeature to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for VisibilityFeature
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            namespace VisibilityFeature {

                /** DefaultSymbolVisibility enum. */
                enum DefaultSymbolVisibility {
                    DEFAULT_SYMBOL_VISIBILITY_UNKNOWN = 0,
                    EXPORT_ALL = 1,
                    EXPORT_TOP_LEVEL = 2,
                    LOCAL_ALL = 3,
                    STRICT = 4
                }
            }
        }

        /** Properties of a FeatureSetDefaults. */
        interface IFeatureSetDefaults {

            /** FeatureSetDefaults defaults */
            defaults?: (google.protobuf.FeatureSetDefaults.IFeatureSetEditionDefault[]|null);

            /** FeatureSetDefaults minimum_edition */
            minimum_edition?: (google.protobuf.Edition|null);

            /** FeatureSetDefaults maximum_edition */
            maximum_edition?: (google.protobuf.Edition|null);
        }

        /** Represents a FeatureSetDefaults. */
        class FeatureSetDefaults implements IFeatureSetDefaults {

            /**
             * Constructs a new FeatureSetDefaults.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IFeatureSetDefaults);

            /** FeatureSetDefaults defaults. */
            public defaults: google.protobuf.FeatureSetDefaults.IFeatureSetEditionDefault[];

            /** FeatureSetDefaults minimum_edition. */
            public minimum_edition: google.protobuf.Edition;

            /** FeatureSetDefaults maximum_edition. */
            public maximum_edition: google.protobuf.Edition;

            /**
             * Encodes the specified FeatureSetDefaults message. Does not implicitly {@link google.protobuf.FeatureSetDefaults.verify|verify} messages.
             * @param message FeatureSetDefaults message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IFeatureSetDefaults, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a FeatureSetDefaults message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns FeatureSetDefaults
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FeatureSetDefaults;

            /**
             * Verifies a FeatureSetDefaults message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a FeatureSetDefaults message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns FeatureSetDefaults
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.FeatureSetDefaults;

            /**
             * Creates a plain object from a FeatureSetDefaults message. Also converts values to other types if specified.
             * @param message FeatureSetDefaults
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.FeatureSetDefaults, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this FeatureSetDefaults to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for FeatureSetDefaults
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace FeatureSetDefaults {

            /** Properties of a FeatureSetEditionDefault. */
            interface IFeatureSetEditionDefault {

                /** FeatureSetEditionDefault edition */
                edition?: (google.protobuf.Edition|null);

                /** FeatureSetEditionDefault overridable_features */
                overridable_features?: (google.protobuf.IFeatureSet|null);

                /** FeatureSetEditionDefault fixed_features */
                fixed_features?: (google.protobuf.IFeatureSet|null);
            }

            /** Represents a FeatureSetEditionDefault. */
            class FeatureSetEditionDefault implements IFeatureSetEditionDefault {

                /**
                 * Constructs a new FeatureSetEditionDefault.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.FeatureSetDefaults.IFeatureSetEditionDefault);

                /** FeatureSetEditionDefault edition. */
                public edition: google.protobuf.Edition;

                /** FeatureSetEditionDefault overridable_features. */
                public overridable_features?: (google.protobuf.IFeatureSet|null);

                /** FeatureSetEditionDefault fixed_features. */
                public fixed_features?: (google.protobuf.IFeatureSet|null);

                /**
                 * Encodes the specified FeatureSetEditionDefault message. Does not implicitly {@link google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault.verify|verify} messages.
                 * @param message FeatureSetEditionDefault message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.FeatureSetDefaults.IFeatureSetEditionDefault, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a FeatureSetEditionDefault message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns FeatureSetEditionDefault
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault;

                /**
                 * Verifies a FeatureSetEditionDefault message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a FeatureSetEditionDefault message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns FeatureSetEditionDefault
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault;

                /**
                 * Creates a plain object from a FeatureSetEditionDefault message. Also converts values to other types if specified.
                 * @param message FeatureSetEditionDefault
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this FeatureSetEditionDefault to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for FeatureSetEditionDefault
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }
        }

        /** Properties of a SourceCodeInfo. */
        interface ISourceCodeInfo {

            /** SourceCodeInfo location */
            location?: (google.protobuf.SourceCodeInfo.ILocation[]|null);
        }

        /** Represents a SourceCodeInfo. */
        class SourceCodeInfo implements ISourceCodeInfo {

            /**
             * Constructs a new SourceCodeInfo.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.ISourceCodeInfo);

            /** SourceCodeInfo location. */
            public location: google.protobuf.SourceCodeInfo.ILocation[];

            /**
             * Encodes the specified SourceCodeInfo message. Does not implicitly {@link google.protobuf.SourceCodeInfo.verify|verify} messages.
             * @param message SourceCodeInfo message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.ISourceCodeInfo, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a SourceCodeInfo message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns SourceCodeInfo
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.SourceCodeInfo;

            /**
             * Verifies a SourceCodeInfo message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a SourceCodeInfo message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns SourceCodeInfo
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.SourceCodeInfo;

            /**
             * Creates a plain object from a SourceCodeInfo message. Also converts values to other types if specified.
             * @param message SourceCodeInfo
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.SourceCodeInfo, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this SourceCodeInfo to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for SourceCodeInfo
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace SourceCodeInfo {

            /** Properties of a Location. */
            interface ILocation {

                /** Location path */
                path?: (number[]|null);

                /** Location span */
                span?: (number[]|null);

                /** Location leading_comments */
                leading_comments?: (string|null);

                /** Location trailing_comments */
                trailing_comments?: (string|null);

                /** Location leading_detached_comments */
                leading_detached_comments?: (string[]|null);
            }

            /** Represents a Location. */
            class Location implements ILocation {

                /**
                 * Constructs a new Location.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.SourceCodeInfo.ILocation);

                /** Location path. */
                public path: number[];

                /** Location span. */
                public span: number[];

                /** Location leading_comments. */
                public leading_comments: string;

                /** Location trailing_comments. */
                public trailing_comments: string;

                /** Location leading_detached_comments. */
                public leading_detached_comments: string[];

                /**
                 * Encodes the specified Location message. Does not implicitly {@link google.protobuf.SourceCodeInfo.Location.verify|verify} messages.
                 * @param message Location message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.SourceCodeInfo.ILocation, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a Location message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns Location
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.SourceCodeInfo.Location;

                /**
                 * Verifies a Location message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a Location message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns Location
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.SourceCodeInfo.Location;

                /**
                 * Creates a plain object from a Location message. Also converts values to other types if specified.
                 * @param message Location
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.SourceCodeInfo.Location, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this Location to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for Location
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }
        }

        /** Properties of a GeneratedCodeInfo. */
        interface IGeneratedCodeInfo {

            /** GeneratedCodeInfo annotation */
            annotation?: (google.protobuf.GeneratedCodeInfo.IAnnotation[]|null);
        }

        /** Represents a GeneratedCodeInfo. */
        class GeneratedCodeInfo implements IGeneratedCodeInfo {

            /**
             * Constructs a new GeneratedCodeInfo.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IGeneratedCodeInfo);

            /** GeneratedCodeInfo annotation. */
            public annotation: google.protobuf.GeneratedCodeInfo.IAnnotation[];

            /**
             * Encodes the specified GeneratedCodeInfo message. Does not implicitly {@link google.protobuf.GeneratedCodeInfo.verify|verify} messages.
             * @param message GeneratedCodeInfo message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IGeneratedCodeInfo, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a GeneratedCodeInfo message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns GeneratedCodeInfo
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.GeneratedCodeInfo;

            /**
             * Verifies a GeneratedCodeInfo message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a GeneratedCodeInfo message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns GeneratedCodeInfo
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.GeneratedCodeInfo;

            /**
             * Creates a plain object from a GeneratedCodeInfo message. Also converts values to other types if specified.
             * @param message GeneratedCodeInfo
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.GeneratedCodeInfo, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this GeneratedCodeInfo to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for GeneratedCodeInfo
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace GeneratedCodeInfo {

            /** Properties of an Annotation. */
            interface IAnnotation {

                /** Annotation path */
                path?: (number[]|null);

                /** Annotation source_file */
                source_file?: (string|null);

                /** Annotation begin */
                begin?: (number|null);

                /** Annotation end */
                end?: (number|null);

                /** Annotation semantic */
                semantic?: (google.protobuf.GeneratedCodeInfo.Annotation.Semantic|null);
            }

            /** Represents an Annotation. */
            class Annotation implements IAnnotation {

                /**
                 * Constructs a new Annotation.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.GeneratedCodeInfo.IAnnotation);

                /** Annotation path. */
                public path: number[];

                /** Annotation source_file. */
                public source_file: string;

                /** Annotation begin. */
                public begin: number;

                /** Annotation end. */
                public end: number;

                /** Annotation semantic. */
                public semantic: google.protobuf.GeneratedCodeInfo.Annotation.Semantic;

                /**
                 * Encodes the specified Annotation message. Does not implicitly {@link google.protobuf.GeneratedCodeInfo.Annotation.verify|verify} messages.
                 * @param message Annotation message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.GeneratedCodeInfo.IAnnotation, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes an Annotation message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns Annotation
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.GeneratedCodeInfo.Annotation;

                /**
                 * Verifies an Annotation message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates an Annotation message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns Annotation
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.GeneratedCodeInfo.Annotation;

                /**
                 * Creates a plain object from an Annotation message. Also converts values to other types if specified.
                 * @param message Annotation
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.GeneratedCodeInfo.Annotation, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this Annotation to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for Annotation
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            namespace Annotation {

                /** Semantic enum. */
                enum Semantic {
                    NONE = 0,
                    SET = 1,
                    ALIAS = 2
                }
            }
        }

        /** SymbolVisibility enum. */
        enum SymbolVisibility {
            VISIBILITY_UNSET = 0,
            VISIBILITY_LOCAL = 1,
            VISIBILITY_EXPORT = 2
        }

        /** Properties of a Duration. */
        interface IDuration {

            /** Duration seconds */
            seconds?: (number|Long|null);

            /** Duration nanos */
            nanos?: (number|null);
        }

        /** Represents a Duration. */
        class Duration implements IDuration {

            /**
             * Constructs a new Duration.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IDuration);

            /** Duration seconds. */
            public seconds: (number|Long);

            /** Duration nanos. */
            public nanos: number;

            /**
             * Encodes the specified Duration message. Does not implicitly {@link google.protobuf.Duration.verify|verify} messages.
             * @param message Duration message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IDuration, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a Duration message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns Duration
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.Duration;

            /**
             * Verifies a Duration message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a Duration message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns Duration
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.Duration;

            /**
             * Creates a plain object from a Duration message. Also converts values to other types if specified.
             * @param message Duration
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.Duration, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this Duration to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for Duration
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a Timestamp. */
        interface ITimestamp {

            /** Timestamp seconds */
            seconds?: (number|Long|null);

            /** Timestamp nanos */
            nanos?: (number|null);
        }

        /** Represents a Timestamp. */
        class Timestamp implements ITimestamp {

            /**
             * Constructs a new Timestamp.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.ITimestamp);

            /** Timestamp seconds. */
            public seconds: (number|Long);

            /** Timestamp nanos. */
            public nanos: number;

            /**
             * Encodes the specified Timestamp message. Does not implicitly {@link google.protobuf.Timestamp.verify|verify} messages.
             * @param message Timestamp message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.ITimestamp, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a Timestamp message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns Timestamp
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.Timestamp;

            /**
             * Verifies a Timestamp message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a Timestamp message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns Timestamp
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.Timestamp;

            /**
             * Creates a plain object from a Timestamp message. Also converts values to other types if specified.
             * @param message Timestamp
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.Timestamp, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this Timestamp to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for Timestamp
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }
    }
}
