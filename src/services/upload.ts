// 上传服务相关的类型定义和API调用

export interface UploadResponse {
  code: number;
  message: string;
  data: {
    filename: string;
    originalname: string;
    path: string;
    size: number;
    mimetype: string;
  };
}

export interface UploadConfig {
  maxFileSize: number;
  allowedTypes: string[];
  uploadPath: string;
}

const API_BASE_URL = 'http://localhost:3003/api';

export class UploadService {
  /**
   * 上传缩略图
   */
  static async uploadThumbnail(file: File): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append('thumbnail', file);

    const response = await fetch(`${API_BASE_URL}/upload/thumbnail`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  }

  /**
   * 删除缩略图
   */
  static async deleteThumbnail(filename: string): Promise<{ code: number; message: string }> {
    const response = await fetch(`${API_BASE_URL}/upload/thumbnail/${filename}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  }

  /**
   * 获取上传配置
   */
  static async getUploadConfig(): Promise<{ code: number; data: UploadConfig }> {
    const response = await fetch(`${API_BASE_URL}/upload/config`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  }

  /**
   * 构建图片完整URL
   */
  static getImageUrl(path: string): string {
    if (!path) return '';
    if (path.startsWith('http')) return path;
    return `http://localhost:3003${path}`;
  }

  /**
   * 从路径中提取文件名
   */
  static getFilenameFromPath(path: string): string {
    if (!path) return '';
    return path.split('/').pop() || '';
  }
}
