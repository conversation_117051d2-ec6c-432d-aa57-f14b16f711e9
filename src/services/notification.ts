import { EventEmitter } from 'events';

export interface TaskNotice {
  id: string;              // 任务ID（这里复用后端返回的 job_id）
  title: string;           // 标题（文件名等）
  progress: number;        // 0-100
  status: 'running' | 'success' | 'error';
  unread?: boolean;        // 是否未读
  createdAt: number;
  updatedAt: number;
}

class NotificationBus extends EventEmitter {
  private tasks: Record<string, TaskNotice> = {};
  private minimized = false;

  getTasks() {
    return Object.values(this.tasks).sort((a, b) => b.updatedAt - a.updatedAt);
  }

  isMinimized() {
    return this.minimized;
  }

  toggleMinimized() {
    this.minimized = !this.minimized;
    this.emit('change');
  }

  upsertTask(task: TaskNotice) {
    this.tasks[task.id] = task;
    this.emit('change');
  }

  startTask(id: string, title: string) {
    const now = Date.now();
    this.upsertTask({ id, title, progress: 0, status: 'running', unread: true, createdAt: now, updatedAt: now });
  }

  updateProgress(id: string, progress: number) {
    const t = this.tasks[id];
    if (!t) return;
    t.progress = Math.min(100, Math.max(0, progress));
    t.updatedAt = Date.now();
    this.emit('change');
  }

  completeTask(id: string) {
    const t = this.tasks[id];
    if (!t) return;
    t.status = 'success';
    t.progress = 100;
    t.unread = true;
    t.updatedAt = Date.now();
    this.emit('change');
  }

  failTask(id: string, reason?: string) {
    const t = this.tasks[id] || { id, title: reason || '任务失败', progress: 0, status: 'error', createdAt: Date.now(), updatedAt: Date.now() } as TaskNotice;
    t.status = 'error';
    t.unread = true;
    t.updatedAt = Date.now();
    this.tasks[id] = t;
    this.emit('change');
  }

  markAllRead() {
    Object.values(this.tasks).forEach(t => { t.unread = false; });
    this.emit('change');
  }
}

export const NotificationService = new NotificationBus();

