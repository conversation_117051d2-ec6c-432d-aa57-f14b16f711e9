import { instance } from '../utils/request';

// 对话组类型定义
export interface DialogueGroup {
  id: string;
  project_id: string;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
}

// 对话数据类型定义
export interface DialogueData {
  id: string;
  dialogue_group_id: string;
  character_name: string;
  gender: string;
  dialogue_text: string;
  generated_audio_file: string;
  duration: number;
  used: boolean;
  needs_audio_regeneration: boolean;
  emotion: string;
  intonation: string;
  volume: number;
  speed: number;
  created_at: string;
  updated_at: string;
}

// 创建对话组请求类型
export interface DialogueGroupCreateData {
  project_id: string;
  name: string;
  description?: string;
}

// 更新对话组请求类型
export interface DialogueGroupUpdateData {
  name?: string;
  description?: string;
}

// 创建对话数据请求类型
export interface DialogueDataCreateData {
  dialogue_group_id: string;
  character_name: string;
  gender: string;
  dialogue_text: string;
  emotion?: string;
  intonation?: string;
  volume?: number;
  speed?: number;
}

// 更新对话数据请求类型
export interface DialogueDataUpdateData {
  character_name?: string;
  gender?: string;
  dialogue_text?: string;
  generated_audio_file?: string;
  duration?: number;
  used?: boolean;
  needs_audio_regeneration?: boolean;
  emotion?: string;
  intonation?: string;
  volume?: number;
  speed?: number;
}

// 对话组服务类
export class DialogueGroupService {
  // 获取对话组列表
  static async list(params: {
    project_id?: string;
    offset?: number;
    limit?: number;
  } = {}) {
    const { offset = 0, limit = 10, ...filters } = params;
    const queryParams = new URLSearchParams({
      offset: offset.toString(),
      limit: limit.toString(),
      ...Object.fromEntries(
        Object.entries(filters).filter(([_, value]) => value !== undefined)
      ),
    });

    const response = await instance.get(`/dialogue-groups?${queryParams}`);
    return {
      data: response.data?.list || [],
      total: response.data?.pagination?.total || 0,
    };
  }

  // 获取对话组详情
  static async getById(id: string) {
    const response = await instance.get(`/dialogue-groups/${id}`);
    return response.data;
  }

  // 创建对话组
  static async create(data: DialogueGroupCreateData) {
    const response = await instance.post('/dialogue-groups', data);
    return response.data;
  }

  // 更新对话组
  static async update(id: string, data: DialogueGroupUpdateData) {
    const response = await instance.put(`/dialogue-groups/${id}`, data);
    return response.data;
  }

  // 删除对话组
  static async delete(id: string) {
    const response = await instance.delete(`/dialogue-groups/${id}`);
    return response.data;
  }

  // 获取项目下的对话组列表
  static async getByProject(projectId: string, params: {
    offset?: number;
    limit?: number;
  } = {}) {
    const { offset = 0, limit = 10 } = params;
    const queryParams = new URLSearchParams({
      offset: offset.toString(),
      limit: limit.toString(),
    });

    const response = await instance.get(`/projects/${projectId}/dialogue-groups?${queryParams}`);
    return response.data;
  }

  // 批量删除对话组
  static async batchDelete(ids: string[]) {
    const response = await instance.post('/dialogue-groups/batch-delete', { ids });
    return response.data;
  }
}

// 对话数据服务类
export class DialogueDataService {
  // 获取对话数据列表
  static async list(params: {
    dialogue_group_id?: string;
    offset?: number;
    limit?: number;
    character_name?: string;
    only_unused?: boolean;
  } = {}) {
    const { offset = 0, limit = 10, ...filters } = params;
    const queryParams = new URLSearchParams({
      offset: offset.toString(),
      limit: limit.toString(),
      ...Object.fromEntries(
        Object.entries(filters).filter(([_, value]) => value !== undefined)
      ),
    });

    const response = await instance.get(`/dialogue-data?${queryParams}`);
    return {
      data: response.data?.list || [],
      total: response.data?.pagination?.total || 0,
    };
  }

  // 获取对话数据详情
  static async getById(id: string) {
    const response = await instance.get(`/dialogue-data/${id}`);
    return response.data;
  }

  // 创建对话数据
  static async create(data: DialogueDataCreateData) {
    const response = await instance.post('/dialogue-data', data);
    return response.data;
  }

  // 更新对话数据
  static async update(id: string, data: DialogueDataUpdateData) {
    const response = await instance.put(`/dialogue-data/${id}`, data);
    return response.data;
  }

  // 删除对话数据
  static async delete(id: string) {
    const response = await instance.delete(`/dialogue-data/${id}`);
    return response.data;
  }

  // 获取对话组下的对话数据列表
  static async getByDialogueGroup(dialogueGroupId: string, params: {
    offset?: number;
    limit?: number;
    character_name?: string;
    only_unused?: boolean;
  } = {}) {
    const { offset = 0, limit = 10, character_name, only_unused } = params;
    const queryParams = new URLSearchParams({
      offset: offset.toString(),
      limit: limit.toString(),
      ...(character_name && { character_name }),
      ...(only_unused !== undefined && { only_unused: only_unused.toString() }),
    });

    const response = await instance.get(`/dialogue-groups/${dialogueGroupId}/dialogue-data?${queryParams}`);
    return response.data;
  }

  // 批量删除对话数据
  static async batchDelete(ids: string[]) {
    const response = await instance.post('/dialogue-data/batch-delete', { ids });
    return response.data;
  }
}

export { DialogueGroupService as default };
