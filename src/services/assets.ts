import { instance } from '../utils/request';

// 资产类型定义
export interface Asset {
  id: string;
  name: string;
  type_id: number;
  type_name: string;
  creator_id: string;
  path: string;
  metadata: string;
  status: number;
  created_at: string;
  updated_at: string;
  thumbnail_path: string;
  thumbnail_fid?: string;
  belveth_fid?: string; // 源文件的 fid
  belveth_filename?: string; // 源文件的完整文件名（包含扩展名）
  source_file_url?: string;
  source_file_size?: number;
}

// 资产版本类型定义
export interface AssetVersion {
  id: string;
  asset_id: string;
  version_number: string;
  path: string;
  metadata: string;
  is_latest: boolean;
  created_at: string;
  updated_at: string;
  thumbnail_path: string;
}

// 创建资产请求类型
export interface AssetCreateData {
  name: string;
  type_id: number;
  metadata?: string; // 将映射到 create_asset 的 data
  thumbnail_path?: string; // 兼容旧逻辑
  thumbnail_fid?: string; // 新的缩略图 fid 字段
  tags?: string[]; // 可选
  source_file_url?: string; // 兼容旧逻辑，不再使用
  source_file_size?: number; // 兼容旧逻辑，不再使用
}

// 更新资产请求类型
export interface AssetUpdateData {
  name?: string;
  type_id?: number;
  metadata?: string;
  status?: number;
  thumbnail_path?: string;
  source_file_url?: string;
  source_file_size?: number;
}

// 创建资产版本请求类型
export interface AssetVersionCreateData {
  asset_id: string;
  version_number: string;
  path: string;
  metadata?: string;
  thumbnail_path?: string;
}

// 将后端 asset_type（仅数字字符串）映射为筛选下拉定义的 ID
function mapAssetTypeToId(t: string | number | undefined): number {
  if (typeof t === 'number') return t;
  const str = String(t || '').trim();
  if (/^\d+$/.test(str)) return parseInt(str, 10);
  // 非法或未知值返回 0（前端展示为“不要展示类别”）
  return 0 as any;
}

// 资产服务类
export class AssetService {
  // 新接口：搜索资产列表 POST /search_assets
  static async searchAssets(body: {
    page: number;
    pageSize: number;
    searchConditionGroups?: Array<{ conds: Array<{ field: string; op: string; values: string }> }>;
  }) {
    // 添加时间戳参数破坏缓存
    const bodyWithTimestamp = {
      ...body,
      _timestamp: Date.now(),
      _random: Math.random().toString(36).substring(2, 15)
    };

    // 添加缓存控制头，强制获取最新数据
    const resp: any = await instance.post('/search_assets', bodyWithTimestamp, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'X-Requested-With': 'XMLHttpRequest',
        'X-Cache-Bust': Date.now().toString()
      }
    });
    const rawList = resp?.assets || resp?.data?.assets || [];
    const total = resp?.total || resp?.data?.total || 0;
    const mapped: Asset[] = rawList.map((a: any) => ({
      id: a.id,
      name: a.name || '',
      type_id: mapAssetTypeToId(a.asset_type),
      type_name: a.asset_type || '',
      creator_id: a.created_by || '',
      path: '',
      metadata: '',
      status: a.state === 1 ? 1 : 2, // 统一 1=启用 2=禁用
      created_at: a.created_at || '',
      updated_at: '',
      thumbnail_path: a.thumbnail_fid || a.belveth_fid || '',
      thumbnail_fid: a.thumbnail_fid || '',
      belveth_fid: a.belveth_fid || '',
      belveth_filename: a.belveth_filename || '', // 源文件完整文件名
      source_file_url: '',
      source_file_size: undefined,
    }));
    return { data: mapped, total };
  }

  // 旧接口保留（兼容其他地方未切换的使用）
  static async list(params: {
    offset?: number;
    limit?: number;
    type_id?: number;
    creator_id?: string;
    status?: number;
    name?: string;
  } = {}) {
    const { offset = 0, limit = 10, ...filters } = params;
    const queryParams = new URLSearchParams({
      offset: offset.toString(),
      limit: limit.toString(),
      ...Object.fromEntries(
        Object.entries(filters).filter(([_, value]) => value !== undefined)
      ),
    });

    const response = await instance.get(`/assets?${queryParams}`);
    return {
      data: response.data?.list || [],
      total: response.data?.pagination?.total || 0,
    };
  }

  // 获取资产详情
  static async getById(id: string) {
    const response = await instance.get(`/assets/${id}`);
    return response.data;
  }

  // 创建资产 -> 直接调用 /create_asset，返回 { id: asset_id }
  static async create(data: AssetCreateData): Promise<{ id: string }> {
    const body: any = {
      asset_type: String(data.type_id),
      data: data.metadata || '',
      tags: data.tags || [],
      thumbnail_fid: data.thumbnail_fid || '',
      name: data.name || '',
    };
    const resp: any = await instance.post('/create_asset', body);
    const assetId = resp?.asset_id || resp?.data?.asset_id || '';
    return { id: assetId };
  }

  // 更新资产
  static async update(id: string, data: AssetUpdateData): Promise<void> {
    const body: any = {
      asset_id: id,
      name: data.name,
      asset_type: String(data.type_id),
      data: data.metadata || '',
      thumbnail_fid: data.thumbnail_path || '',
    };
    await instance.post('/update_asset', body);
  }

  // 删除资产
  static async delete(id: string): Promise<void> {
    const body = {
      asset_id: id,
    };
    await instance.post('/delete_asset', body);
  }

  // 获取资产版本列表
  static async getVersions(assetId: string, params: {
    offset?: number;
    limit?: number;
  } = {}) {
    const { offset = 0, limit = 10 } = params;
    const queryParams = new URLSearchParams({
      offset: offset.toString(),
      limit: limit.toString(),
    });

    const response = await instance.get(`/assets/${assetId}/versions?${queryParams}`);
    return {
      data: response.data?.list || [],
      total: response.data?.pagination?.total || 0,
    };
  }

  // 创建资产版本
  static async createVersion(data: AssetVersionCreateData) {
    const response = await instance.post('/asset-versions', data);
    return response.data;
  }

  // 获取资产版本详情
  static async getVersionById(versionId: string) {
    const response = await instance.get(`/asset-versions/${versionId}`);
    return response.data;
  }

  // 设置为最新版本
  static async setLatestVersion(versionId: string) {
    const response = await instance.put(`/asset-versions/${versionId}/set-latest`);
    return response.data;
  }

  // 删除资产版本
  static async deleteVersion(versionId: string) {
    const response = await instance.delete(`/asset-versions/${versionId}`);
    return response.data;
  }

  // 获取资产类型列表
  static async getAssetTypes() {
    const response = await instance.get('/asset-types');
    return {
      data: response.data?.list || [],
      total: response.data?.pagination?.total || 0,
    };
  }

  // 场景资产管理
  static async addToScene(sceneId: string, assetId: string, order: number = 0) {
    const response = await instance.post('/scene-assets', {
      scene_id: sceneId,
      asset_id: assetId,
      asset_order: order,
    });
    return response.data;
  }

  static async removeFromScene(sceneId: string, assetId: string) {
    const response = await instance.delete(`/scene-assets/${sceneId}/${assetId}`);
    return response.data;
  }

  // 获取资产详情：GET /get_asset?asset_id=
  static async getAssetById(assetId: string) {
    const resp: any = await instance.get('/get_asset', { params: { asset_id: assetId } });
    const a = resp?.asset || resp?.data?.asset || resp || {};
    return a;
  }

  static async getSceneAssets(sceneId: string, params: {
    offset?: number;
    limit?: number;
    asset_type_id?: number;
  } = {}) {
    const { offset = 0, limit = 10, asset_type_id } = params;
    const queryParams = new URLSearchParams({
      offset: offset.toString(),
      limit: limit.toString(),
      ...(asset_type_id && { asset_type_id: asset_type_id.toString() }),
    });

    const response = await instance.get(`/scenes/${sceneId}/assets?${queryParams}`);
    return {
      data: response.data?.list || [],
      total: response.data?.pagination?.total || 0,
    };
  }



  // 镜头资产管理
  static async addToShot(shotId: string, assetId: string, order: number = 0) {
    const response = await instance.post('/shot-assets', {
      shot_id: shotId,
      asset_id: assetId,
      asset_order: order,
    });
    return response.data;
  }

  static async removeFromShot(shotId: string, assetId: string) {
    const response = await instance.delete(`/shot-assets/${shotId}/${assetId}`);
    return response.data;
  }

  static async getShotAssets(shotId: string, params: {
    offset?: number;
    limit?: number;
    asset_type_id?: number;
  } = {}) {
    const { offset = 0, limit = 10, asset_type_id } = params;
    const queryParams = new URLSearchParams({
      offset: offset.toString(),
      limit: limit.toString(),
      ...(asset_type_id && { asset_type_id: asset_type_id.toString() }),
    });

    const response = await instance.get(`/shots/${shotId}/assets?${queryParams}`);
    return {
      data: response.data?.list || [],
      total: response.data?.pagination?.total || 0,
    };
  }
}
