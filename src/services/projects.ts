import { instance } from '../utils/request';

export interface Project {
  id: string;
  name: string;
  description?: string;
  status: number;
  created_at: string | number; // 可能为时间戳
  updated_at: string | number; // 可能为时间戳
  thumbnail_path?: string;
  thumbnail_fid?: string;
  created_by?: string;
  updated_by?: string;
  available_asset_ids?: string[];
  assets?: any[]; // 关联的角色资产
}

export interface ProjectCreateData {
  name: string;
  description?: string;
  creator_id: string;
  status?: number;
  thumbnail_path?: string;
  episode?: number;
  dialogue_group?: string;
  assetIds?: string[]; // 角色资产ID数组
}

export interface ProjectUpdateData {
  name?: string;
  description?: string;
  status?: number;
  thumbnail_path?: string;
  episode?: number;
  dialogue_group?: string;
  assetIds?: string[]; // 角色资产ID数组
}

export interface ProjectListParams {
  page?: number;
  limit?: number;
  search?: string; // 关键词
}

export interface ProjectListResponse {
  list: Project[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * 项目API服务
 */
export class ProjectService {
  /**
   * 获取项目列表
   */
  static async getList(params: ProjectListParams = {}): Promise<ProjectListResponse> {
    const { page = 1, limit = 10, search } = params;
    // 构造后端要求结构：POST /core/search_project_list
    const body: any = {
      pagination: {
        page: Number(page),
        page_size: Number(limit),
      },
      sort: 1 as number,
    };
    if (search && search.trim()) {
      body.filters = [
        {
          field: 'name',
          operation: 0,
          value: search.trim(),
        },
      ];
    }

    // 避免服务端把数字解析成字符串导致枚举报错，显式确保数字类型
    if (typeof body.sort !== 'number') body.sort = Number(body.sort) || 1;
    if (typeof body.pagination?.page !== 'number') body.pagination.page = Number(body.pagination?.page) || 1;
    if (typeof body.pagination?.page_size !== 'number')
      body.pagination.page_size = Number(body.pagination?.page_size) || Number(limit) || 10;

    // 使用 POST /core/search_project_list
    const respData: any = await instance.request({
      url: '/core/search_project_list',
      method: 'POST',
      data: body,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
    // 兼容响应结构：{ project: [...], rsp: { code, msg } }
    const listRaw = respData?.project || respData?.data?.project || [];
    const mappedList: Project[] = listRaw.map((p: any) => ({
      id: p.id,
      name: p.name,
      description: p.description || '',
      creator_id: p.creator_id || '',
      status: p.status ?? 1,
      created_at: p.created_at || '',
      updated_at: p.updated_at || '',
      // 同时保留 thumbnail_fid 并映射到 thumbnail_path 以供渲染
      thumbnail_fid: p.thumbnail_fid || '',
      thumbnail_path: p.thumbnail_fid || p.thumbnail_path || '',
      created_by: p.created_by || '',
      updated_by: p.updated_by || '',
      available_asset_ids: p.available_asset_ids || [],
      assets: p.assets || [],
    }));

    const total = mappedList.length; // 后端暂未给总数时退化
    return {
      list: mappedList,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit) || 0,
        hasNext: total > page * limit,
        hasPrev: page > 1,
      },
    };
  }

  /**
   * 获取项目详情
   */
  static async getById(id: string): Promise<Project> {
    // 新接口：GET /core/get_project?project=（统一使用 instance，自动带 Authorization）
    const respData: any = await instance.get('/core/get_project', { params: { project: id } });
    const projectData = respData?.project || respData?.data?.project;

    if (!projectData) {
      throw new Error('项目数据不存在');
    }

    // 获取完整的资产信息
    let assets: any[] = [];
    const availableAssetIds = projectData.available_asset_ids || [];

    if (availableAssetIds.length > 0) {
      try {
        // 过滤掉空字符串的资产ID
        const validAssetIds = availableAssetIds.filter((id: string) => id && id.trim() !== '');

        if (validAssetIds.length > 0) {
          // 导入AssetService来获取资产详情
          const { AssetService } = await import('./assets.js');

          // 获取角色资产列表，然后筛选出匹配的资产
          const assetResponse = await AssetService.searchAssets({
            page: 1,
            pageSize: 100,
            searchConditionGroups: [
              { conds: [{ field: 'asset_type', op: 'EQ', values: '1' }] } // 角色模型类型
            ]
          });

          // 筛选出匹配的资产
          assets = (assetResponse.data || []).filter((asset: any) =>
            validAssetIds.includes(asset.id)
          ).map((asset: any) => ({
            id: asset.id,
            name: asset.data || asset.name || '未知资产',
            thumbnail_path: asset.thumbnail_fid || asset.belveth_fid || '',
            source_file_url: asset.source_file_url || '',
            source_file_size: asset.source_file_size || 0,
            type_id: 1, // 角色模型
          }));
        }
      } catch (error) {
        console.error('获取资产详情失败:', error);
        // 如果获取失败，至少保留ID信息
        assets = availableAssetIds
          .filter((id: string) => id && id.trim() !== '')
          .map((assetId: string) => ({ id: assetId, name: '加载失败' }));
      }
    }

    // 映射响应数据，确保字段正确
    return {
      id: projectData.id,
      name: projectData.name,
      description: projectData.description || '',
      status: projectData.status ?? 1,
      created_at: projectData.created_at || '',
      updated_at: projectData.updated_at || '',
      thumbnail_fid: projectData.thumbnail_fid || '',
      thumbnail_path: projectData.thumbnail_fid || projectData.thumbnail_path || '',
      created_by: projectData.created_by || '',
      updated_by: projectData.updated_by || '',
      available_asset_ids: projectData.available_asset_ids || [],
      // 使用获取到的完整资产信息
      assets: assets,
    };
  }

  /**
   * 创建项目
   */
  static async create(data: any): Promise<any> {
    return instance.post('/core/create_project', data);
  }

  /**
   * 更新项目
   */
  static async update(id: string, data: any): Promise<any> {
    // 新的更新接口：POST /core/update_project，需携带 project_id
    return instance.post('/core/update_project', { project_id: id, ...data });
  }

  /**
   * 删除项目 -> 调用 delete_project 接口
   */
  static async delete(id: string): Promise<void> {
    await instance.post('/core/delete_project', { project_id: id });
  }

  /**
   * 批量删除项目
   */
  static async batchDelete(ids: string[]): Promise<any> {
    const response = await instance.post('/projects/batch-delete', { ids });
    return response.data;
  }

  /**
   * 获取项目下的故事板列表
   */
  static async getStoryboards(projectId: string, params: { page?: number; limit?: number } = {}): Promise<any> {
    const response = await instance.get(`/projects/${projectId}/storyboards`, { params });
    return response.data;
  }
}

export default ProjectService;
