import { instance } from '../utils/request';

export interface Project {
  id: string;
  name: string;
  description?: string;
  status: number;
  created_at: string | number; // 可能为时间戳
  updated_at: string | number; // 可能为时间戳
  thumbnail_path?: string;
  thumbnail_fid?: string;
  created_by?: string;
  updated_by?: string;
  available_asset_ids?: string[];
  assets?: any[]; // 关联的角色资产
}

export interface ProjectCreateData {
  name: string;
  description?: string;
  creator_id: string;
  status?: number;
  thumbnail_path?: string;
  episode?: number;
  dialogue_group?: string;
  assetIds?: string[]; // 角色资产ID数组
}

export interface ProjectUpdateData {
  name?: string;
  description?: string;
  status?: number;
  thumbnail_path?: string;
  episode?: number;
  dialogue_group?: string;
  assetIds?: string[]; // 角色资产ID数组
}

export interface ProjectListParams {
  page?: number;
  limit?: number;
  search?: string; // 关键词
}

export interface ProjectListResponse {
  list: Project[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * 项目API服务
 */
export class ProjectService {
  /**
   * 获取项目列表
   */
  static async getList(params: ProjectListParams = {}): Promise<ProjectListResponse> {
    const { page = 1, limit = 10, search } = params;
    // 构造后端要求结构：POST /core/search_project_list
    const body: any = {
      pagination: {
        page: Number(page),
        page_size: Number(limit),
      },
      sort: 1 as number,
    };
    if (search && search.trim()) {
      body.filters = [
        {
          field: 'name',
          operation: 0,
          value: search.trim(),
        },
      ];
    }

    // 避免服务端把数字解析成字符串导致枚举报错，显式确保数字类型
    if (typeof body.sort !== 'number') body.sort = Number(body.sort) || 1;
    if (typeof body.pagination?.page !== 'number') body.pagination.page = Number(body.pagination?.page) || 1;
    if (typeof body.pagination?.page_size !== 'number')
      body.pagination.page_size = Number(body.pagination?.page_size) || Number(limit) || 10;

    // 使用 POST /core/search_project_list
    const respData: any = await instance.request({
      url: '/core/search_project_list',
      method: 'POST',
      data: body,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
    // 兼容响应结构：{ project: [...], rsp: { code, msg } }
    const listRaw = respData?.project || respData?.data?.project || [];
    const mappedList: Project[] = listRaw.map((p: any) => ({
      id: p.id,
      name: p.name,
      description: p.description || '',
      creator_id: p.creator_id || '',
      status: p.status ?? 1,
      created_at: p.created_at || '',
      updated_at: p.updated_at || '',
      // 同时保留 thumbnail_fid 并映射到 thumbnail_path 以供渲染
      thumbnail_fid: p.thumbnail_fid || '',
      thumbnail_path: p.thumbnail_fid || p.thumbnail_path || '',
      created_by: p.created_by || '',
      updated_by: p.updated_by || '',
      available_asset_ids: p.available_asset_ids || [],
      assets: p.assets || [],
    }));

    const total = mappedList.length; // 后端暂未给总数时退化
    return {
      list: mappedList,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit) || 0,
        hasNext: total > page * limit,
        hasPrev: page > 1,
      },
    };
  }

  /**
   * 获取项目详情
   */
  static async getById(id: string): Promise<Project> {
    // 新接口：GET /core/get_project?project=（统一使用 instance，自动带 Authorization）
    const respData: any = await instance.get('/core/get_project', { params: { project: id } });
    return respData?.project || respData?.data?.project;
  }

  /**
   * 创建项目
   */
  static async create(data: any): Promise<any> {
    return instance.post('/core/create_project', data);
  }

  /**
   * 更新项目
   */
  static async update(id: string, data: any): Promise<any> {
    // 新的更新接口：POST /core/update_project，需携带 project_id
    return instance.post('/core/update_project', { project_id: id, ...data });
  }

  /**
   * 删除项目 -> 调用 delete_project 接口
   */
  static async delete(id: string): Promise<void> {
    await instance.post('/core/delete_project', { project_id: id });
  }

  /**
   * 批量删除项目
   */
  static async batchDelete(ids: string[]): Promise<any> {
    const response = await instance.post('/projects/batch-delete', { ids });
    return response.data;
  }

  /**
   * 获取项目下的故事板列表
   */
  static async getStoryboards(projectId: string, params: { page?: number; limit?: number } = {}): Promise<any> {
    const response = await instance.get(`/projects/${projectId}/storyboards`, { params });
    return response.data;
  }
}

export default ProjectService;
