import React, { useState, useRef } from 'react';
import { Button, Progress, Space, MessagePlugin, Tag } from 'tdesign-react';
import { UploadIcon, DeleteIcon, CheckCircleIcon, CloseCircleIcon } from 'tdesign-icons-react';
import { FileUploadService } from '../../services/fileUpload';

interface FileUploadProps {
  value?: string;
  onChange?: (value: string, fileKey: string, taskId?: string) => void;
  module: string; // 模块名称：assets, scenes, project_roles
  accept?: string;
  disabled?: boolean;
  maxSize?: number; // 最大文件大小，单位：字节
  placeholder?: string;
}

interface UploadState {
  uploading: boolean;
  progress: number;
  fileName: string;
  fileSize: number;
  error?: string;
  completed: boolean;
  taskId?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({
  value,
  onChange,
  module,
  accept = '*/*',
  disabled = false,
  maxSize = 10 * 1024 * 1024 * 1024, // 默认10GB
  placeholder = '选择文件上传'
}) => {
  const [uploadState, setUploadState] = useState<UploadState>({
    uploading: false,
    progress: 0,
    fileName: '',
    fileSize: 0,
    completed: false
  });

  const fileInputRef = useRef<HTMLInputElement>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 处理文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 检查文件大小
    if (file.size > maxSize) {
      MessagePlugin.error(`文件大小不能超过 ${formatFileSize(maxSize)}`);
      return;
    }

    startUpload(file);
  };

  // 开始上传
  const startUpload = async (file: File) => {
    try {
      setUploadState({
        uploading: true,
        progress: 0,
        fileName: file.name,
        fileSize: file.size,
        completed: false,
        error: undefined
      });

      // 创建取消控制器
      abortControllerRef.current = new AbortController();

      const result = await FileUploadService.uploadFile(
        file,
        module,
        (progress) => {
          setUploadState(prev => ({
            ...prev,
            progress
          }));
        },
        (error) => {
          setUploadState(prev => ({
            ...prev,
            error: error.message,
            uploading: false
          }));
        },
        abortControllerRef.current
      );

      // 上传成功
      setUploadState(prev => ({
        ...prev,
        uploading: false,
        progress: 100,
        completed: true,
        taskId: result.taskId
      }));

      onChange?.(result.fileUrl, result.fileKey, result.taskId);
      MessagePlugin.success(`文件上传成功 (${formatFileSize(file.size)})`);
    } catch (error) {
      const errorMessage = (error as Error).message;

      // 如果是用户主动取消，不显示错误消息
      if (errorMessage === '上传已取消') {
        console.log('用户主动取消上传');
        return;
      }

      console.error('文件上传失败:', error);
      setUploadState(prev => ({
        ...prev,
        uploading: false,
        error: errorMessage
      }));
      MessagePlugin.error('文件上传失败');
    } finally {
      // 清理AbortController引用
      abortControllerRef.current = null;
    }
  };

  // 取消上传
  const handleCancel = async () => {
    if (uploadState.uploading && abortControllerRef.current) {
      try {
        // 调用AbortController取消上传
        abortControllerRef.current.abort();

        // 立即更新UI状态
        setUploadState(prev => ({
          ...prev,
          uploading: false,
          error: '上传已取消'
        }));

        // 清理AbortController引用
        abortControllerRef.current = null;

        MessagePlugin.info('上传已取消');
      } catch (error) {
        console.error('取消上传失败:', error);
        MessagePlugin.error('取消上传失败');
      }
    }
  };

  // 重新上传
  const handleRetry = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // 清除文件
  const handleClear = () => {
    setUploadState({
      uploading: false,
      progress: 0,
      fileName: '',
      fileSize: 0,
      completed: false
    });
    onChange?.('', '');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 选择文件
  const handleSelectFile = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div style={{ width: '100%' }}>
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleFileSelect}
        style={{ display: 'none' }}
        disabled={disabled}
      />

      {!uploadState.fileName && !value && (
        <div
          style={{
            border: '2px dashed #d9d9d9',
            borderRadius: '6px',
            padding: '40px 20px',
            textAlign: 'center',
            backgroundColor: '#fafafa',
            cursor: disabled ? 'not-allowed' : 'pointer',
            transition: 'border-color 0.3s'
          }}
          onClick={disabled ? undefined : handleSelectFile}
          onDragOver={(e) => {
            e.preventDefault();
            if (!disabled) {
              e.currentTarget.style.borderColor = '#0052d9';
            }
          }}
          onDragLeave={(e) => {
            e.preventDefault();
            e.currentTarget.style.borderColor = '#d9d9d9';
          }}
          onDrop={(e) => {
            e.preventDefault();
            e.currentTarget.style.borderColor = '#d9d9d9';
            if (disabled) return;
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
              const file = files[0];
              if (file.size > maxSize) {
                MessagePlugin.error(`文件大小不能超过 ${formatFileSize(maxSize)}`);
                return;
              }
              startUpload(file);
            }
          }}
        >
          <UploadIcon size="32px" style={{ color: '#999', marginBottom: '12px' }} />
          <div style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>
            {placeholder}
          </div>
          <div style={{ fontSize: '12px', color: '#999' }}>
            支持拖拽上传，文件大小不超过 {formatFileSize(maxSize)}
          </div>
        </div>
      )}

      {(uploadState.fileName || value) && (
        <div
          style={{
            border: '1px solid #e7e7e7',
            borderRadius: '6px',
            padding: '16px',
            backgroundColor: '#fff'
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '12px' }}>
            <div style={{ flex: 1, minWidth: 0 }}>
              <div style={{ 
                fontSize: '14px', 
                fontWeight: 500, 
                marginBottom: '4px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}>
                {uploadState.fileName || (value ? value.split('/').pop() : '')}
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>
                {uploadState.fileSize > 0 ? formatFileSize(uploadState.fileSize) : ''}
              </div>
            </div>
            
            <div style={{ marginLeft: '12px' }}>
              {uploadState.uploading && (
                <Tag color="processing">上传中</Tag>
              )}
              {uploadState.completed && (
                <Tag color="success" icon={<CheckCircleIcon />}>已完成</Tag>
              )}
              {uploadState.error && (
                <Tag color="danger" icon={<CloseCircleIcon />}>失败</Tag>
              )}
              {value && !uploadState.uploading && !uploadState.error && (
                <Tag color="success" icon={<CheckCircleIcon />}>已上传</Tag>
              )}
            </div>
          </div>

          {uploadState.uploading && (
            <div style={{ marginBottom: '12px' }}>
              <Progress
                percentage={uploadState.progress}
                size="small"
                status={uploadState.error ? 'error' : 'active'}
                label={`${uploadState.progress.toFixed(1)}%`}
              />
              <div style={{
                fontSize: '12px',
                color: '#666',
                marginTop: '4px',
                textAlign: 'center'
              }}>
                {uploadState.progress < 100 ? '正在上传...' : '处理中...'}
              </div>
            </div>
          )}

          {uploadState.error && (
            <div style={{ 
              fontSize: '12px', 
              color: '#e34d59', 
              marginBottom: '12px',
              padding: '8px',
              backgroundColor: '#fef0f0',
              borderRadius: '4px'
            }}>
              {uploadState.error}
            </div>
          )}

          <Space>
            {uploadState.uploading && (
              <Button size="small" theme="default" onClick={handleCancel}>
                取消上传
              </Button>
            )}
            {uploadState.error && (
              <Button size="small" theme="primary" onClick={handleRetry}>
                重新上传
              </Button>
            )}
            {!uploadState.uploading && (
              <Button size="small" theme="default" onClick={handleSelectFile} disabled={disabled}>
                重新选择
              </Button>
            )}
            <Button 
              size="small" 
              theme="danger" 
              variant="outline"
              icon={<DeleteIcon />}
              onClick={handleClear}
              disabled={disabled || uploadState.uploading}
            >
              删除
            </Button>
          </Space>
        </div>
      )}
    </div>
  );
};

export default FileUpload;
