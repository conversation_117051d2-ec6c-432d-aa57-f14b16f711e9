import React, { useState, useEffect } from 'react';
import { Button, Space, MessagePlugin, Popconfirm, Tag } from 'tdesign-react';
import { DownloadIcon, SwapIcon, FileIcon, DeleteIcon } from 'tdesign-icons-react';
import FileUpload from '../FileUpload';
import { buildFileUrl, buildDownloadUrlByFid } from '../../utils/url';

interface SourceFileManagerProps {
  value?: {
    url?: string;
    size?: number;
    filename?: string; // 文件名，用于下载时的name参数
  };
  onChange?: (fileUrl: string, fileKey: string, taskId?: string) => void;
  module: string; // 模块名称：assets, scenes, project_roles
  accept?: string;
  disabled?: boolean;
  readOnly?: boolean; // 只读模式：只显示文件信息和下载按钮，不显示替换和删除按钮
  placeholder?: string;
}

const SourceFileManager: React.FC<SourceFileManagerProps> = ({
  value,
  onChange,
  module,
  accept = '*/*',
  disabled = false,
  readOnly = false,
  placeholder = '上传源文件'
}) => {
  const [showUpload, setShowUpload] = useState(!value?.url);

  // 监听value变化，更新showUpload状态
  useEffect(() => {
    setShowUpload(!value?.url);
  }, [value?.url]);

  // 格式化文件大小
  const formatFileSize = (bytes?: number): string => {
    if (!bytes || bytes === 0) return '未知大小';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 获取文件名
  const getFileName = (url?: string): string => {
    if (!url) return '未知文件';
    const parts = url.split('/');
    return parts[parts.length - 1] || '未知文件';
  };

  // 下载文件
  const handleDownload = async () => {
    if (!value?.url) return;

    try {
      MessagePlugin.loading('正在准备下载...', 0);

      // 构建下载URL，如果是fid格式且有filename，则使用新的下载接口
      let downloadUrl: string;
      const url = value.url;
      const filename = value.filename;

      // 检查是否是fid格式（不含协议/斜杠）
      if (!/^https?:\/\//i.test(url) && !url.startsWith('/')) {
        // 使用fid下载接口，包含name参数
        downloadUrl = buildDownloadUrlByFid(url, filename);
      } else {
        // 使用原有的文件URL构建函数
        downloadUrl = buildFileUrl(url);
      }

      // 使用fetch获取文件，支持大文件下载
      const response = await fetch(downloadUrl);
      if (!response.ok) {
        throw new Error(`下载失败: ${response.status}`);
      }

      // 获取文件blob
      const blob = await response.blob();
      const fileName = filename || getFileName(url);

      // 创建下载链接
      const blobUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = fileName;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 清理URL对象
      window.URL.revokeObjectURL(blobUrl);

      MessagePlugin.closeAll();
      MessagePlugin.success('文件下载成功');
    } catch (error) {
      console.error('下载失败:', error);
      MessagePlugin.closeAll();
      MessagePlugin.error('下载失败，请稍后重试');
    }
  };

  // 更换文件
  const handleReplace = () => {
    setShowUpload(true);
  };

  // 删除文件
  const handleDelete = () => {
    onChange?.('', '', undefined);
    setShowUpload(true);
    MessagePlugin.success('文件已删除');
  };

  // 文件上传成功
  const handleUploadSuccess = (fileUrl: string, fileKey: string, taskId?: string) => {
    onChange?.(fileUrl, fileKey, taskId);
    setShowUpload(false);
    // 不在这里显示成功消息，由FileUpload组件负责
  };

  // 取消上传
  const handleCancelUpload = () => {
    if (value?.url) {
      setShowUpload(false);
    }
  };

  if (showUpload) {
    return (
      <div style={{ width: '100%' }}>
        <FileUpload
          module={module}
          accept={accept}
          disabled={disabled}
          placeholder={placeholder}
          onChange={handleUploadSuccess}
        />
        {value?.url && (
          <div style={{ marginTop: '8px' }}>
            <Button 
              variant="text" 
              size="small" 
              onClick={handleCancelUpload}
            >
              取消上传
            </Button>
          </div>
        )}
      </div>
    );
  }

  if (!value?.url) {
    return (
      <div style={{ width: '100%' }}>
        {disabled ? (
          <div style={{
            padding: '16px',
            textAlign: 'center',
            color: '#999',
            backgroundColor: '#f5f5f5',
            borderRadius: '6px',
            border: '1px dashed #d9d9d9'
          }}>
            暂无源文件
          </div>
        ) : (
          <FileUpload
            module={module}
            accept={accept}
            disabled={disabled}
            placeholder={placeholder}
            onChange={handleUploadSuccess}
          />
        )}
      </div>
    );
  }

  return (
    <div
      style={{
        border: '1px solid #e7e7e7',
        borderRadius: '6px',
        padding: '16px',
        backgroundColor: '#fff',
        width: '100%'
      }}
    >
      {/* 文件信息展示 */}
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
        <FileIcon size="24px" style={{ color: '#0052d9', marginRight: '8px' }} />
        <div style={{ flex: 1, minWidth: 0 }}>
          <div
            style={{
              fontSize: '14px',
              fontWeight: 500,
              color: '#333',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              marginBottom: '4px'
            }}
            title={getFileName(value.url)}
          >
            {getFileName(value.url)}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            <Tag size="small" variant="light">
              {formatFileSize(value.size)}
            </Tag>
          </div>
        </div>
      </div>

      {/* 操作按钮 */}
      <Space size="small">
        <Button
          theme="primary"
          variant="outline"
          size="small"
          icon={<DownloadIcon />}
          onClick={handleDownload}
          disabled={disabled}
        >
          下载
        </Button>
        {!readOnly && (
          <>
            <Button
              theme="default"
              variant="outline"
              size="small"
              icon={<SwapIcon />}
              onClick={handleReplace}
              disabled={disabled}
            >
              更换
            </Button>
            {disabled ? (
              <Button
                theme="danger"
                variant="outline"
                size="small"
                icon={<DeleteIcon />}
                disabled={disabled}
              >
                删除
              </Button>
            ) : (
              <Popconfirm
                content="确定要删除此文件吗？"
                onConfirm={handleDelete}
              >
                <Button
                  theme="danger"
                  variant="outline"
                  size="small"
                  icon={<DeleteIcon />}
                  disabled={disabled}
                >
                  删除
                </Button>
              </Popconfirm>
            )}
          </>
        )}
      </Space>

      {/* 文件URL（可选显示） */}
      <div style={{ marginTop: '8px', fontSize: '11px', color: '#999', wordBreak: 'break-all' }}>
        {value.url}
      </div>
    </div>
  );
};

export default SourceFileManager;
