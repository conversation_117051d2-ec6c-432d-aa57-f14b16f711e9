import React, { useState } from 'react';
import { Button, Progress, Space, NotificationPlugin } from 'tdesign-react';
import { DownloadIcon, PoweroffIcon, PlayCircleIcon, CloseIcon } from 'tdesign-icons-react';
import { instance } from '../../utils/request';

interface FileDownloaderProps {
  belvethFid: string;
  defaultFileName?: string;
}

interface DownloadState {
  status: 'idle' | 'downloading' | 'paused' | 'completed' | 'error';
  progress: number;
  transferredSize: number;
  totalSize: number;
  speed?: number;
  error?: string;
  taskId?: string;
}

const FileDownloader: React.FC<FileDownloaderProps> = ({
  belvethFid,
  defaultFileName = '未知文件',
}) => {
  const [downloadState, setDownloadState] = useState<DownloadState>({
    status: 'idle',
    progress: 0,
    transferredSize: 0,
    totalSize: 0,
  });

  // 格式化文件大小
  const formatSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化速度
  const formatSpeed = (bytesPerSecond: number) => {
    return formatSize(bytesPerSecond) + '/s';
  };

  // 开始下载
  const startDownload = async () => {
    try {
      setDownloadState(prev => ({ ...prev, status: 'downloading', progress: 0, transferredSize: 0, error: undefined }));

      // 首先获取文件信息
      const fileInfoResponse = await instance.get(`/file_info/${belvethFid}`);
      const fileInfo = fileInfoResponse.data || fileInfoResponse;
      const fileName = fileInfo.filename || defaultFileName;
      const fileSize = fileInfo.size || 0;

      setDownloadState(prev => ({ ...prev, totalSize: fileSize }));

      // 开始下载文件
      const downloadResponse = await instance.get(`/download_file/${belvethFid}`, {
        responseType: 'blob',
        onDownloadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const progress = Math.round((progressEvent.loaded / progressEvent.total) * 100);
            const speed = progressEvent.loaded / ((Date.now() - startTime) / 1000);
            
            setDownloadState(prev => ({
              ...prev,
              progress,
              transferredSize: progressEvent.loaded,
              totalSize: progressEvent.total || 0,
              speed,
            }));

            // 进度更新已在 setDownloadState 中处理
          }
        },
      });

      const startTime = Date.now();

      // 创建下载链接
      const blob = new Blob([downloadResponse.data]);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      setDownloadState(prev => ({ ...prev, status: 'completed', progress: 100 }));

      NotificationPlugin.success({
        title: '下载完成',
        content: `文件 "${fileName}" 下载完成`,
        duration: 3000,
      });

    } catch (error) {
      const errorMessage = (error as any)?.message || '下载失败';
      setDownloadState(prev => ({ ...prev, status: 'error', error: errorMessage }));

      NotificationPlugin.error({
        title: '下载失败',
        content: errorMessage,
        duration: 5000,
      });
    }
  };

  // 暂停下载（实际上无法暂停，这里只是UI状态）
  const pauseDownload = () => {
    setDownloadState(prev => ({ ...prev, status: 'paused' }));
  };

  // 恢复下载（重新开始）
  const resumeDownload = () => {
    startDownload();
  };

  // 取消下载
  const cancelDownload = () => {
    setDownloadState({
      status: 'idle',
      progress: 0,
      transferredSize: 0,
      totalSize: 0,
    });
  };

  return (
    <div style={{ width: '100%' }}>
      {downloadState.status === 'idle' && (
        <Button
          theme="primary"
          variant="outline"
          icon={<DownloadIcon />}
          onClick={startDownload}
          size="small"
        >
          下载文件
        </Button>
      )}

      {downloadState.status === 'downloading' && (
        <div style={{ width: '100%' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
            <span style={{ fontSize: '12px', color: '#666' }}>
              {formatSize(downloadState.transferredSize)} / {formatSize(downloadState.totalSize)}
              {downloadState.speed && (
                <span style={{ marginLeft: '8px' }}>
                  {formatSpeed(downloadState.speed)}
                </span>
              )}
            </span>
            <Space>
              <Button size="small" variant="text" icon={<PoweroffIcon />} onClick={pauseDownload} />
              <Button size="small" variant="text" icon={<CloseIcon />} onClick={cancelDownload} />
            </Space>
          </div>
          <Progress percentage={downloadState.progress} size="small" />
        </div>
      )}

      {downloadState.status === 'paused' && (
        <div style={{ width: '100%' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
            <span style={{ fontSize: '12px', color: '#666' }}>
              已暂停 - {formatSize(downloadState.transferredSize)} / {formatSize(downloadState.totalSize)}
            </span>
            <Space>
              <Button size="small" variant="text" icon={<PlayCircleIcon />} onClick={resumeDownload} />
              <Button size="small" variant="text" icon={<CloseIcon />} onClick={cancelDownload} />
            </Space>
          </div>
          <Progress percentage={downloadState.progress} size="small" theme="warning" />
        </div>
      )}

      {downloadState.status === 'completed' && (
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span style={{ fontSize: '12px', color: '#4caf50' }}>
            下载完成 - {formatSize(downloadState.totalSize)}
          </span>
          <Button size="small" variant="text" onClick={() => startDownload()}>
            重新下载
          </Button>
        </div>
      )}

      {downloadState.status === 'error' && (
        <div style={{ width: '100%' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
            <span style={{ fontSize: '12px', color: '#f44336' }}>
              下载失败: {downloadState.error}
            </span>
            <Space>
              <Button size="small" variant="text" onClick={resumeDownload}>
                重试
              </Button>
              <Button size="small" variant="text" icon={<CloseIcon />} onClick={cancelDownload} />
            </Space>
          </div>
          <Progress percentage={downloadState.progress} size="small" theme="danger" />
        </div>
      )}
    </div>
  );
};

export default FileDownloader;
