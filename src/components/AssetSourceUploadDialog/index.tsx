import React, { useState } from 'react';
import { Dialog, Upload, MessagePlugin, Space, Divider } from 'tdesign-react';
import { FileIcon } from 'tdesign-icons-react';
import { uploadAssetSourceInBackground } from '../../services/assetUpload';

interface Props {
  visible: boolean;
  assetId: string;
  assetName: string;
  onClose: () => void;
  onStartUpload?: (fileName: string, fileSize: number, totalChunks?: number) => string; // 启动传输管理器任务
  onUpdateProgress?: (taskId: string, progress: number, transferredSize: number, speed?: number) => void;
  onTaskComplete?: (taskId: string) => void;
  onTaskError?: (taskId: string, error: string) => void;
  onUpdateJobId?: (taskId: string, jobId: string) => void; // 更新任务的jobId
  onRegisterController?: (taskId: string, abortController: AbortController) => void; // 注册上传控制器
  onUpdateTaskInfo?: (taskId: string, fileName: string, fileSize: number, totalChunks: number) => void; // 更新任务基本信息（用于重试时）
  resumeTaskId?: string; // 用于断点续传的任务ID
  resumeFromChunk?: number; // 从哪个分片开始恢复
  checkAssetUploading?: (assetId: string) => boolean; // 检查资产是否正在上传的函数
  canCreateNewUploadTask?: () => boolean; // 检查是否可以创建新的上传任务
  getRunningUploadCount?: () => number; // 获取当前运行的上传任务数量
  maxConcurrentUploads?: number; // 最大并发上传数量
}

const AssetSourceUploadDialog: React.FC<Props> = ({
  visible,
  assetId,
  assetName,
  onClose,
  onStartUpload,
  onUpdateProgress,
  onTaskComplete,
  onTaskError,
  onUpdateJobId,
  onRegisterController,
  onUpdateTaskInfo,
  resumeTaskId,
  resumeFromChunk,
  checkAssetUploading,
  canCreateNewUploadTask,
  getRunningUploadCount,
  maxConcurrentUploads
}) => {
  const [files, setFiles] = useState<any[]>([]);
  const [uploading, setUploading] = useState(false);
  const [abortController, setAbortController] = useState<AbortController | null>(null);
  const [waitingUploadFiles, setWaitingUploadFiles] = useState<any[]>([]);
  const uploadRef = React.useRef<any>(null);

  // 断点续传模式检测
  const isResumeMode = Boolean(resumeTaskId && resumeFromChunk && resumeFromChunk > 0);
  // 重试模式检测（resumeFromChunk = 0 表示重试）
  const isRetryMode = Boolean(resumeTaskId && resumeFromChunk === 0);

  // 检查是否有选择的文件
  const hasSelectedFile = React.useMemo(() => {
    // 优先检查待上传文件，其次检查已选择文件
    const pickFile = (item: any): File | null => (item instanceof File ? item : (item?.raw instanceof File ? item.raw : null));
    const targetFile = waitingUploadFiles.length > 0
      ? pickFile(waitingUploadFiles[0])
      : (files.length > 0 ? pickFile(files[0]) : null);
    return targetFile instanceof File;
  }, [files, waitingUploadFiles]);

  // 计算实际的上传状态
  // 如果当前资产已有上传任务在进行，则禁用按钮
  // 如果当前对话框正在上传，也禁用按钮
  // 重试模式下，忽略资产上传状态检查，允许用户重新上传
  // 如果没有选择文件，也禁用按钮
  const isAssetCurrentlyUploading = (checkAssetUploading && !isRetryMode) ? checkAssetUploading(assetId) : false;
  const actualUploading = uploading || isAssetCurrentlyUploading || !hasSelectedFile;


  // 计算文件MD5哈希值
  const calculateFileMD5 = React.useCallback(async (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = async (event) => {
        try {
          const arrayBuffer = event.target?.result as ArrayBuffer;
          const hashBuffer = await crypto.subtle.digest('SHA-256', arrayBuffer);
          const hashArray = Array.from(new Uint8Array(hashBuffer));
          const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
          resolve(hashHex);
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = () => reject(new Error('文件读取失败'));
      reader.readAsArrayBuffer(file);
    });
  }, []);

  // 验证文件是否与原文件一致（用于断点续传）
  const validateResumeFile = React.useCallback(async (file: File): Promise<boolean> => {
    if (!resumeTaskId) return true;

    try {
      // 从localStorage获取原文件信息
      const resumeKey = `upload_resume_${resumeTaskId}`;
      const resumeDataStr = localStorage.getItem(resumeKey);

      if (!resumeDataStr) {
        MessagePlugin.error('缺少断点续传数据');
        return false;
      }

      const resumeData = JSON.parse(resumeDataStr);

      // 1. 验证文件基本信息（快速检查）
      if (file.name !== resumeData.fileName) {
        MessagePlugin.error(`文件名不匹配，期望: ${resumeData.fileName}，实际: ${file.name}`);
        return false;
      }

      if (file.size !== resumeData.fileSize) {
        MessagePlugin.error(`文件大小不匹配，期望: ${resumeData.fileSize}，实际: ${file.size}`);
        return false;
      }

      // 2. 验证文件哈希值（严格检查）
      if (resumeData.fileHash) {
        MessagePlugin.info('正在验证文件完整性，请稍候...');

        try {
          const currentFileHash = await calculateFileMD5(file);

          if (currentFileHash !== resumeData.fileHash) {
            MessagePlugin.error('文件内容不匹配，请选择原始文件以确保断点续传的正确性');
            return false;
          }

          MessagePlugin.success('文件验证通过，可以继续上传');
        } catch (hashError) {
          console.warn('文件哈希计算失败，跳过哈希验证:', hashError);
          MessagePlugin.warning('无法验证文件完整性，但基本信息匹配，将继续上传');
        }
      } else {
        MessagePlugin.warning('缺少文件哈希信息，仅验证了基本信息');
      }

      return true;
    } catch (error) {
      console.error('验证文件失败:', error);
      MessagePlugin.error('验证文件失败');
      return false;
    }
  }, [resumeTaskId, calculateFileMD5]);

  const handleChange = (fileList: any[]) => {
    setFiles(fileList);
  };

  // 处理待上传文件变化
  const onWaitingUploadFilesChange = (params: { files: any[] }) => {
    setWaitingUploadFiles(params.files);
  };



  // 重置状态
  const resetState = (forceAbort = false) => {
    setFiles([]);
    setUploading(false);
    if (abortController && forceAbort) {
      abortController.abort();
      setAbortController(null);
    }
  };

  // 监听assetId变化，切换资产时重置状态
  React.useEffect(() => {
    // 当assetId变化时，重置uploading状态（但不重置文件选择）
    // 这样可以支持多个资产并发上传
    setUploading(false);
    setAbortController(null);
  }, [assetId]);

  // 监听visible变化，关闭时重置状态
  React.useEffect(() => {
    if (!visible) {
      // 只有在没有正在进行的上传时才重置状态
      if (!uploading) {
        resetState(true); // 强制abort
      } else {
        // 如果正在上传，只重置UI状态，不abort上传
        setFiles([]);
      }
    }
  }, [visible, uploading]);

  const handleOk = async () => {
    // 优先从待上传文件中获取，其次从已选择文件中获取
    // tdesign-react 的 Upload 组件：files 列表项可能是 { raw: File } 或 File 本身
    const pickFile = (item: any): File | null => (item instanceof File ? item : (item?.raw instanceof File ? item.raw : null));
    const targetFile = waitingUploadFiles.length > 0
      ? pickFile(waitingUploadFiles[0])
      : (files.length > 0 ? pickFile(files[0]) : null);

    if (!targetFile) {
      MessagePlugin.warning('请选择源文件');
      return;
    }
    if (!(targetFile instanceof File)) {
      MessagePlugin.error('选择的文件无效，请重新选择');
      return;
    }
    try {
      setUploading(true);
      const file = targetFile as File;

      // 如果是断点续传模式，验证文件一致性
      if (isResumeMode) {
        const isValid = await validateResumeFile(file);
        if (!isValid) {
          setUploading(false);
          return;
        }
      }


      // 计算分片总数
      const DEFAULT_CHUNK_SIZE = 5 * 1024 * 1024; // 5MB
      const totalChunks = Math.ceil(file.size / DEFAULT_CHUNK_SIZE);

      let taskId = '';
      let controller: AbortController;

      if (isResumeMode && resumeTaskId) {
        // 断点续传模式：复用现有任务
        taskId = resumeTaskId;
        controller = new AbortController();
        setAbortController(controller);

        // 注册控制器到传输管理器（覆盖之前的控制器）
        if (onRegisterController) {
          onRegisterController(taskId, controller);
        }
      } else if (isRetryMode && resumeTaskId) {
        // 重试模式：复用现有任务ID，但作为新上传处理
        taskId = resumeTaskId;
        controller = new AbortController();
        setAbortController(controller);

        // 重试模式下，更新任务的基本信息（文件名、大小、分片数量）
        if (onUpdateTaskInfo) {
          onUpdateTaskInfo(taskId, file.name, file.size, totalChunks);
        }

        // 注册控制器到传输管理器（覆盖之前的控制器）
        if (onRegisterController) {
          onRegisterController(taskId, controller);
        }
      } else {
        // 新上传模式：创建新任务
        // 检查并发限制（仅对新任务）
        if (canCreateNewUploadTask && !canCreateNewUploadTask()) {
          const runningCount = getRunningUploadCount ? getRunningUploadCount() : 0;
          const maxCount = maxConcurrentUploads || 10;
          MessagePlugin.warning(`当前并发上传任务已达上限（${runningCount}/${maxCount}），请等待其他任务完成后再试`);
          setUploading(false);
          return;
        }

        if (onStartUpload) {
          taskId = onStartUpload(file.name, file.size, totalChunks);
        }

        controller = new AbortController();
        setAbortController(controller);

        // 注册控制器到传输管理器
        if (taskId && onRegisterController) {
          onRegisterController(taskId, controller);
        }
      }

      // 后台执行真实分片上传流程
      onClose();
      uploadAssetSourceInBackground({
        file,
        assetId,
        assetName,
        abortController: controller,
        taskId: taskId,
        resumeFromChunk: isResumeMode ? resumeFromChunk : undefined, // 只在断点续传时传入，重试模式不传入
        onProgress: (progress, transferredSize, speed) => {
          if (taskId && onUpdateProgress) {
            onUpdateProgress(taskId, progress, transferredSize, speed);
          }
        },
        onJobIdReceived: (jobId) => {
          if (taskId && onUpdateJobId) {
            onUpdateJobId(taskId, jobId);
          }
        },
        onComplete: () => {
          setAbortController(null);
          setUploading(false); // 上传完成后重置uploading状态
          if (taskId && onTaskComplete) {
            onTaskComplete(taskId);
          }
        },
        onError: (error) => {
          setAbortController(null);
          setUploading(false); // 上传失败后重置uploading状态
          if (taskId && onTaskError) {
            onTaskError(taskId, error);
          }
        },
        onCancel: () => {
          setAbortController(null);
          setUploading(false); // 上传取消后重置uploading状态
        }
      }).catch(() => {
        setUploading(false); // 异常情况下也要重置uploading状态
      });
    } catch (e: any) {
      MessagePlugin.error(e?.message || '启动上传失败');
      setUploading(false);
    }
  };

  return (
    <Dialog
      visible={visible}
      header={isRetryMode ? "重试上传源文件" : (isResumeMode ? "断点续传源文件" : "上传源文件")}
      onConfirm={handleOk}
      confirmBtn={{
        loading: actualUploading,
        content: isRetryMode ? '重新上传' : (isResumeMode ? '继续上传' : '开始上传')
      }}
      onCancel={onClose}
      onClose={onClose}
      width={600}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ marginBottom: 16 }}>
          <h4 style={{ margin: '0 0 8px 0', fontSize: 16, fontWeight: 500 }}>
            <FileIcon style={{ marginRight: 8, verticalAlign: 'middle' }} />
            {isRetryMode ? `重试上传资产 "${assetName}" 的源文件` : `为资产 "${assetName}" 上传源文件`}
          </h4>
          <div style={{ color: '#666', fontSize: 14 }}>
            {isRetryMode
              ? '之前的上传任务已失败，请重新选择文件进行上传'
              : '支持各种格式的源文件，上传后将在后台处理'
            }
          </div>
        </div>

        <Divider />

        <Upload
          ref={uploadRef}
          autoUpload={false}
          files={files}
          onChange={handleChange}
          multiple={false}
          accept="*"
          showUploadProgress={false}
          onWaitingUploadFilesChange={onWaitingUploadFilesChange}
          placeholder={files.length > 0 ? undefined : "这是一段没有文件时的占位文本"}
        />

        <div style={{
          padding: 12,
          backgroundColor: '#f0f9ff',
          border: '1px solid #bae7ff',
          borderRadius: 6,
          fontSize: 14,
          color: '#0066cc'
        }}>
          <strong>提示：</strong>
          {isRetryMode
            ? '选择文件后，点击"重新上传"即可重新开始上传任务，之前的失败任务将被替换。'
            : '选择文件后，点击"开始上传"即可在后台开始上传，您可以继续其他操作。上传进度将在传输管理器中显示。'
          }
        </div>
      </Space>
    </Dialog>
  );
};

export default AssetSourceUploadDialog;