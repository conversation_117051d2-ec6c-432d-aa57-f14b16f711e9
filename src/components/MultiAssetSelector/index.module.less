.multi-asset-selector {
  .asset-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 16px;
  }

  .asset-card {
    cursor: pointer;
    border: 1px solid #e7e7e7;
    border-radius: 6px;
    padding: 16px;
    min-height: 200px;
    position: relative;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &.selected {
      border: 2px solid #0052d9;
      background-color: #f0f8ff;
      box-shadow: 0 4px 12px rgba(0, 82, 217, 0.2);
    }
  }

  .selected-assets {
    margin-bottom: 16px;
    
    .selected-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      padding: 12px;
      background-color: #f8f9fa;
      border-radius: 6px;
      max-height: 120px;
      overflow-y: auto;
    }
  }
}
