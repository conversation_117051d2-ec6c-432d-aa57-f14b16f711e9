import React, { useMemo, useState, useCallback, useRef, useEffect } from 'react';
import { Table, TableProps } from 'tdesign-react';
import { RenderOptimizer } from '../../utils/performanceOptimizer';

interface VirtualizedTableProps<T = any> extends Omit<TableProps, 'data'> {
  data: T[];
  itemHeight?: number;
  containerHeight?: number;
  bufferSize?: number;
}

/**
 * 虚拟化表格组件 - 解决大数据量渲染性能问题
 * 基于 Chrome DevTools 分析，优化表格渲染性能
 */
const VirtualizedTable = <T = any>({
  data,
  itemHeight = 50,
  containerHeight = 400,
  bufferSize = 5,
  ...tableProps
}: VirtualizedTableProps<T>) => {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  // 计算可见项目范围
  const visibleRange = useMemo(() => {
    return RenderOptimizer.calculateVisibleItems(
      containerHeight,
      itemHeight,
      scrollTop,
      data.length,
      bufferSize
    );
  }, [containerHeight, itemHeight, scrollTop, data.length, bufferSize]);

  // 获取可见数据
  const visibleData = useMemo(() => {
    return data.slice(visibleRange.startIndex, visibleRange.endIndex + 1);
  }, [data, visibleRange.startIndex, visibleRange.endIndex]);

  // 节流滚动处理
  const handleScroll = useCallback(
    RenderOptimizer.throttle((event: React.UIEvent<HTMLDivElement>) => {
      setScrollTop(event.currentTarget.scrollTop);
    }, 16), // 60fps
    []
  );

  // 计算偏移量
  const offsetY = visibleRange.startIndex * itemHeight;
  const totalHeight = data.length * itemHeight;

  return (
    <div
      ref={containerRef}
      style={{
        height: containerHeight,
        overflow: 'auto',
        position: 'relative',
      }}
      onScroll={handleScroll}
    >
      {/* 占位空间 */}
      <div style={{ height: totalHeight, position: 'relative' }}>
        {/* 可见内容 */}
        <div
          style={{
            transform: `translateY(${offsetY}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
          }}
        >
          <Table
            {...tableProps}
            data={visibleData}
            pagination={false}
            maxHeight={undefined}
          />
        </div>
      </div>
    </div>
  );
};

export default React.memo(VirtualizedTable) as <T = any>(props: VirtualizedTableProps<T>) => JSX.Element;
