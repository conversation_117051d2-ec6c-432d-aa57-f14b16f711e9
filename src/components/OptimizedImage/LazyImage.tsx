import React, { useState, useRef, useEffect, useCallback } from 'react';
import { MemoryOptimizer } from '../../utils/performanceOptimizer';

interface LazyImageProps {
  src: string;
  alt?: string;
  width?: number;
  height?: number;
  placeholder?: string;
  defaultImage?: string; // 默认图片，当src为空或加载失败时显示
  className?: string;
  style?: React.CSSProperties;
  onLoad?: () => void;
  onError?: () => void;
  // 懒加载配置
  rootMargin?: string; // 提前加载的距离
  threshold?: number;  // 触发加载的阈值
}

/**
 * 优化的懒加载图片组件
 * 解决图片加载导致的性能问题
 */
const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt = '',
  width,
  height,
  placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvYWRpbmcuLi48L3RleHQ+PC9zdmc+',
  defaultImage,
  className,
  style,
  onLoad,
  onError,
  rootMargin = '50px 0px', // 提前50px开始加载
  threshold = 0.01,       // 1%可见时触发
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // 检查缓存
  const cachedSrc = MemoryOptimizer.getCache(`img_${src}`);

  // 确定要显示的图片源
  const displaySrc = src || defaultImage || '';

  // 创建 Intersection Observer - 观察容器元素
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // 如果有缓存，直接设置为已加载状态
    if (cachedSrc) {
      setIsInView(true);
      setIsLoaded(true);
      return;
    }

    // 创建 Intersection Observer
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            // 一旦进入视口就停止观察，避免重复触发
            observer.unobserve(entry.target);
          }
        });
      },
      {
        rootMargin, // 提前加载距离
        threshold,  // 触发阈值
      }
    );

    observer.observe(container);
    observerRef.current = observer;

    return () => {
      observer.disconnect();
    };
  }, [src, cachedSrc, rootMargin, threshold]);

  // 处理图片加载成功
  const handleLoad = useCallback(() => {
    setIsLoaded(true);
    setIsLoading(false);
    setHasError(false);

    // 缓存成功加载的图片
    MemoryOptimizer.setCache(`img_${src}`, src);

    onLoad?.();
  }, [src, onLoad]);

  // 处理图片加载错误
  const handleError = useCallback(() => {
    setHasError(true);
    setIsLoaded(false);
    setIsLoading(false);
    onError?.();
  }, [onError]);

  // 图片预加载逻辑
  useEffect(() => {
    // 如果不在视口内，不进行加载
    if (!isInView) return;

    // 如果已经加载过或正在加载，跳过
    if (isLoaded || isLoading || hasError) return;

    // 确定要加载的图片源
    const imageToLoad = src || defaultImage;

    // 如果没有任何图片源，直接显示占位符
    if (!imageToLoad || !imageToLoad.trim()) {
      setIsLoaded(true); // 设置为已加载状态，显示占位符
      return;
    }

    setIsLoading(true);

    // 使用Image对象预加载
    const img = new Image();

    img.onload = () => {
      setIsLoaded(true);
      setIsLoading(false);
      setHasError(false);
      // 只缓存原始src，不缓存默认图片
      if (src && src.trim()) {
        MemoryOptimizer.setCache(`img_${src}`, src);
      }
    };

    img.onerror = () => {
      // 如果是原图片加载失败且有默认图片，尝试加载默认图片
      if (src && src.trim() && defaultImage && imageToLoad === src) {
        const defaultImg = new Image();
        defaultImg.onload = () => {
          setIsLoaded(true);
          setIsLoading(false);
          setHasError(false);
        };
        defaultImg.onerror = () => {
          setHasError(true);
          setIsLoading(false);
          setIsLoaded(false);
        };
        defaultImg.src = defaultImage;
      } else {
        setHasError(true);
        setIsLoading(false);
        setIsLoaded(false);
      }
    };

    img.src = imageToLoad;

    // 清理函数
    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [isInView, src, defaultImage, isLoaded, isLoading, hasError]);

  // 样式定义
  const containerStyle: React.CSSProperties = {
    position: 'relative',
    width,
    height,
    overflow: 'hidden',
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
    ...style,
  };

  const placeholderStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    position: 'absolute',
    top: 0,
    left: 0,
    opacity: isLoaded ? 0 : 1,
    transition: 'opacity 0.3s ease-in-out',
    zIndex: 1,
  };

  const imageStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    position: 'absolute',
    top: 0,
    left: 0,
    opacity: isLoaded ? 1 : 0,
    transition: 'opacity 0.3s ease-in-out',
    zIndex: 2,
  };

  const loadingStyle: React.CSSProperties = {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f5f5f5',
    color: '#999',
    fontSize: '12px',
    zIndex: 3,
  };

  return (
    <div
      ref={containerRef}
      className={className}
      style={containerStyle}
    >
      {/* 占位图 - 始终显示，通过透明度控制 */}
      <img
        src={placeholder}
        alt=""
        style={placeholderStyle}
      />

      {/* 实际图片 - 只有在视口内或有缓存时才渲染 */}
      {(isInView || cachedSrc) && displaySrc && !hasError && (
        <img
          src={cachedSrc || displaySrc}
          alt={alt}
          style={imageStyle}
          onLoad={handleLoad}
          onError={handleError}
        />
      )}

      {/* 加载状态指示器 */}
      {isLoading && (
        <div style={loadingStyle}>
          <div style={{
            width: '16px',
            height: '16px',
            border: '2px solid #e0e0e0',
            borderTop: '2px solid #1976d2',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
          }} />
        </div>
      )}

      {/* 错误状态 */}
      {hasError && (
        <div style={loadingStyle}>
          <span>加载失败</span>
        </div>
      )}
    </div>
  );
};

export default React.memo(LazyImage);
