import React, { useEffect, useState } from 'react';
import ReactD<PERSON> from 'react-dom';
import { Card, Button, Tag, Badge, Progress, Dialog } from 'tdesign-react';
import { NotificationService, TaskNotice } from '../../services/notification';
import { BrowseIcon, CloseIcon, ViewListIcon } from 'tdesign-icons-react';

const rootId = 'upload-task-notifier-root';

function ensureRoot() {
  let root = document.getElementById(rootId);
  if (!root) {
    root = document.createElement('div');
    root.id = rootId;
    document.body.appendChild(root);
  }
  return root;
}

const TaskCard: React.FC<{
  tasks: TaskNotice[];
  minimized: boolean;
  onToggle: () => void;
  onOpenDetail: (task: TaskNotice) => void;
}> = ({ tasks, minimized, onToggle, onOpenDetail }) => {
  if (minimized) {
    const unread = tasks.filter(t => t.unread).length;
    const running = tasks.filter(t => t.status === 'running').length;
    return (
      <div
        style={{
          width: 64,
          height: 64,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          backgroundColor: '#fff',
          border: '1px solid #e7e7e7',
          borderRadius: 8,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          transition: 'all 0.2s'
        }}
        onClick={onToggle}
        onMouseEnter={(e) => e.currentTarget.style.transform = 'scale(1.05)'}
        onMouseLeave={(e) => e.currentTarget.style.transform = 'scale(1)'}
      >
        <Badge count={unread} dot={unread > 0}>
          <div style={{ textAlign: 'center' }}>
            <ViewListIcon size="24px" />
            {running > 0 && <div style={{ fontSize: 10, color: '#0052d9' }}>{running}个进行中</div>}
          </div>
        </Badge>
      </div>
    );
  }

  return (
    <Card bordered style={{ width: 380, maxHeight: 480 }} header={
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <span>后台任务 ({tasks.length})</span>
        <Button variant="text" size="small" onClick={onToggle} icon={<CloseIcon />}>最小化</Button>
      </div>
    }>
      <div style={{ maxHeight: 360, overflowY: 'auto' }}>
        {tasks.length === 0 ? (
          <div style={{ color: '#999', textAlign: 'center', padding: 20 }}>暂无任务</div>
        ) : tasks.map(t => (
          <div key={t.id} style={{
            border: '1px solid #e7e7e7',
            borderRadius: 6,
            padding: 12,
            marginBottom: 8,
            cursor: 'pointer',
            transition: 'all 0.2s'
          }} onClick={() => onOpenDetail(t)}
          onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f8f9fa'}
          onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 8 }}>
              <div style={{ flex: 1, minWidth: 0 }}>
                <div style={{ fontSize: 14, fontWeight: 600, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>{t.title}</div>
                <div style={{ fontSize: 12, color: '#666', marginTop: 2 }}>
                  {new Date(t.updatedAt).toLocaleTimeString()}
                </div>
              </div>
              <Tag theme={t.status === 'running' ? 'primary' : t.status === 'success' ? 'success' : 'danger'} size="small">
                {t.status === 'running' ? '进行中' : t.status === 'success' ? '完成' : '失败'}
              </Tag>
            </div>
            {t.status === 'running' && (
              <Progress percentage={t.progress} size="small" />
            )}
          </div>
        ))}
      </div>
      <div style={{ borderTop: '1px solid #e7e7e7', paddingTop: 8, marginTop: 8, textAlign: 'center' }}>
        <Button variant="text" size="small" onClick={() => NotificationService.markAllRead()}>
          标记全部已读
        </Button>
      </div>
    </Card>
  );
};

const TaskDetailDialog: React.FC<{ task: TaskNotice | null; onClose: () => void }> = ({ task, onClose }) => {
  if (!task) return null;

  return (
    <Dialog visible={!!task} header="任务详情" onClose={onClose} width={480}>
      <div style={{ padding: '0 8px' }}>
        <div style={{ marginBottom: 16 }}>
          <div style={{ fontSize: 16, fontWeight: 600, marginBottom: 8 }}>{task.title}</div>
          <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
            <Tag theme={task.status === 'running' ? 'primary' : task.status === 'success' ? 'success' : 'danger'}>
              {task.status === 'running' ? '进行中' : task.status === 'success' ? '完成' : '失败'}
            </Tag>
            <span style={{ fontSize: 14, color: '#666' }}>
              {task.status === 'running' ? `进度: ${task.progress}%` :
               task.status === 'success' ? '上传成功' : '上传失败'}
            </span>
          </div>
        </div>

        {task.status === 'running' && (
          <div style={{ marginBottom: 16 }}>
            <Progress percentage={task.progress} />
          </div>
        )}

        <div style={{ fontSize: 12, color: '#999' }}>
          <div>开始时间: {new Date(task.createdAt).toLocaleString()}</div>
          <div>更新时间: {new Date(task.updatedAt).toLocaleString()}</div>
          <div>任务ID: {task.id}</div>
        </div>
      </div>
    </Dialog>
  );
};

const UploadTaskNotifier: React.FC = () => {
  const [tasks, setTasks] = useState<TaskNotice[]>(NotificationService.getTasks());
  const [minimized, setMinimized] = useState(NotificationService.isMinimized());
  const [detailTask, setDetailTask] = useState<TaskNotice | null>(null);

  useEffect(() => {
    const rerender = () => {
      setTasks(NotificationService.getTasks());
      setMinimized(NotificationService.isMinimized());
    };
    NotificationService.on('change', rerender);
    return () => { NotificationService.off('change', rerender); };
  }, []);

  return (
    <>
      {ReactDOM.createPortal(
        <div style={{ position: 'fixed', right: 20, bottom: 20, zIndex: 9999 }}>
          <TaskCard
            tasks={tasks}
            minimized={minimized}
            onToggle={() => NotificationService.toggleMinimized()}
            onOpenDetail={setDetailTask}
          />
        </div>,
        ensureRoot()
      )}
      <TaskDetailDialog task={detailTask} onClose={() => setDetailTask(null)} />
    </>
  );
};

export default UploadTaskNotifier;

