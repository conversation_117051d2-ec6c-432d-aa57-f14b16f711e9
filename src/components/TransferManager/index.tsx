import React, { useState, useEffect } from 'react';
import { Card, Button, Progress, Tag, Space, Tabs, Badge, NotificationPlugin } from 'tdesign-react';
import { CloseIcon, SettingIcon, RemoveIcon, DeleteIcon, RefreshIcon, PoweroffIcon, PlayCircleIcon } from 'tdesign-icons-react';

export interface TransferTask {
  id: string;
  name: string;
  type: 'upload' | 'download';
  status: 'pending' | 'running' | 'completed' | 'failed' | 'paused';
  progress: number;
  size: number;
  transferredSize: number;
  speed?: number;
  error?: string;
  createdAt: Date;
  completedAt?: Date;
  belvethFid?: string; // 用于下载任务的文件ID
  belvethFilename?: string; // 用于下载任务的完整文件名
  assetId?: string; // 用于上传任务的资产ID
  jobId?: string; // 用于上传任务的作业ID
  totalChunks?: number; // 用于上传任务的分片总数
}

interface TransferManagerProps {
  visible: boolean;
  onClose: () => void; // 保留用于最小化
  tasks: TransferTask[];
  onTaskAction: (taskId: string, action: 'pause' | 'resume' | 'retry' | 'cancel') => void;
  onClearCompleted: () => void;
  onClearFailed: () => void;
  onPauseAll: () => void;
  onResumeAll: () => void;
  onRetryFailed: () => void;
  minimizedByDefault?: boolean; // 新增：默认最小化
  maxConcurrentUploads?: number; // 最大并发上传数量
}

const TransferManager: React.FC<TransferManagerProps> = ({
  visible,
  onClose,
  tasks,
  onTaskAction,
  onClearCompleted,
  onClearFailed,
  onPauseAll,
  onResumeAll,
  onRetryFailed,
  minimizedByDefault,
  maxConcurrentUploads = 10,
}) => {
  const [isMinimized, setIsMinimized] = useState(!!minimizedByDefault);
  const [activeTab, setActiveTab] = useState('all');

  // 统计各状态任务数量
  const stats = {
    running: tasks.filter(t => t.status === 'running').length,
    completed: tasks.filter(t => t.status === 'completed').length,
    failed: tasks.filter(t => t.status === 'failed').length,
    total: tasks.length,
    // 并发上传统计
    runningUploads: tasks.filter(t => t.type === 'upload' && (t.status === 'running' || t.status === 'pending')).length,
  };

  // 根据标签页筛选任务
  const filteredTasks = tasks.filter(task => {
    switch (activeTab) {
      case 'upload':
        return task.type === 'upload';
      case 'download':
        return task.type === 'download';
      case 'completed':
        return task.status === 'completed';
      case 'failed':
        return task.status === 'failed';
      default:
        return true;
    }
  });

  // 格式化文件大小
  const formatSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化速度
  const formatSpeed = (bytesPerSecond: number) => {
    return formatSize(bytesPerSecond) + '/s';
  };

  // 获取状态标签
  const getStatusTag = (status: TransferTask['status']) => {
    const statusMap = {
      pending: { theme: 'default' as const, text: '等待中' },
      running: { theme: 'primary' as const, text: '传输中' },
      completed: { theme: 'success' as const, text: '已完成' },
      failed: { theme: 'danger' as const, text: '失败' },
      paused: { theme: 'warning' as const, text: '已暂停' },
    };
    const config = statusMap[status];
    return <Tag theme={config.theme}>{config.text}</Tag>;
  };

  // 渲染任务列表
  const renderTaskList = (taskList: TransferTask[]) => {
    if (taskList.length === 0) {
      return (
        <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
          暂无任务
        </div>
      );
    }

    return (
      <div style={{ padding: '12px 0' }}>
        {taskList.map(task => (
          <div
            key={task.id}
            style={{
              marginBottom: '12px',
              padding: '16px',
              border: '1px solid #e7e7e7',
              borderRadius: 6,
              backgroundColor: '#fff',
            }}
          >
            {/* 任务头部 */}
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{ fontWeight: 600, fontSize: '14px' }}>{task.name}</span>
                {getStatusTag(task.status)}
                <Tag theme={task.type === 'upload' ? 'primary' : 'success'}>
                  {task.type === 'upload' ? '上传' : '下载'}
                </Tag>
              </div>
              <Space>
                {task.status === 'running' && (
                  <Button
                    size="small"
                    variant="text"
                    icon={<PoweroffIcon />}
                    onClick={() => onTaskAction(task.id, 'pause')}
                  />
                )}
                {task.status === 'paused' && (
                  <Button
                    size="small"
                    variant="text"
                    icon={<PlayCircleIcon />}
                    onClick={() => onTaskAction(task.id, 'resume')}
                  />
                )}
                {task.status === 'failed' && (
                  <Button
                    size="small"
                    variant="text"
                    icon={<RefreshIcon />}
                    onClick={() => onTaskAction(task.id, 'retry')}
                  />
                )}
                <Button
                  size="small"
                  variant="text"
                  theme="danger"
                  icon={<CloseIcon />}
                  onClick={() => onTaskAction(task.id, 'cancel')}
                />
              </Space>
            </div>

            {/* 进度条 */}
            {task.status !== 'completed' && (
              <div style={{ marginBottom: '8px' }}>
                <Progress
                  percentage={task.progress}
                  status={task.status === 'failed' ? 'error' : 'active'}
                  size="small"
                />
              </div>
            )}

            {/* 任务详情 */}
            <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px', color: '#666' }}>
              <div>
                {formatSize(task.transferredSize)} / {formatSize(task.size)}
                {task.speed && task.status === 'running' && (
                  <span style={{ marginLeft: '8px' }}>
                    {formatSpeed(task.speed)}
                  </span>
                )}
              </div>
              <div>
                {task.status === 'completed' && task.completedAt && (
                  <span>完成于 {task.completedAt.toLocaleTimeString()}</span>
                )}
                {task.status === 'failed' && task.error && (
                  <span style={{ color: '#f44336' }}>错误: {task.error}</span>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  // 最小化状态的显示
  if (isMinimized) {
    return (
      <div
        style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          zIndex: 1000,
          cursor: 'pointer',
          backgroundColor: '#fff',
          border: '1px solid #e7e7e7',
          borderRadius: 8,
          padding: '12px 16px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
          transition: 'all 0.3s ease',
        }}
        onClick={() => setIsMinimized(false)}
        onMouseEnter={(e) => {
          e.currentTarget.style.transform = 'scale(1.05)';
          e.currentTarget.style.boxShadow = '0 6px 16px rgba(0,0,0,0.2)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.transform = 'scale(1)';
          e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span style={{ fontWeight: 600, color: '#1976d2' }}>传输管理器</span>
          {stats.running > 0 && (
            <Badge count={stats.running} size="small">
              <Tag theme="primary">活跃传输</Tag>
            </Badge>
          )}
          {stats.completed > 0 && (
            <Badge count={stats.completed} size="small">
              <Tag theme="success">已完成</Tag>
            </Badge>
          )}
          {stats.failed > 0 && (
            <Badge count={stats.failed} size="small">
              <Tag theme="danger">失败</Tag>
            </Badge>
          )}
        </div>
      </div>
    );
  }

  if (!visible) return null;

  return (
    <div
      style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        width: '480px',
        height: '600px',
        zIndex: 1000,
        backgroundColor: '#fff',
        border: '1px solid #e7e7e7',
        borderRadius: 8,
        boxShadow: '0 8px 24px rgba(0,0,0,0.15)',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {/* 标题栏 */}
      <div
        style={{
          padding: '16px 20px',
          borderBottom: '1px solid #e7e7e7',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: '#fafafa',
          borderRadius: '8px 8px 0 0',
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <h3 style={{ margin: 0, fontSize: '16px', fontWeight: 600 }}>传输管理器</h3>
          <div style={{ display: 'flex', gap: '8px' }}>
            <Badge count={stats.running} size="small">
              <span style={{ fontSize: '12px', color: '#1976d2' }}>活跃传输</span>
            </Badge>
            <Badge count={`${stats.runningUploads}/${maxConcurrentUploads}`} size="small">
              <span style={{ fontSize: '12px', color: '#ff9800' }}>并发上传</span>
            </Badge>
            <Badge count={stats.completed} size="small">
              <span style={{ fontSize: '12px', color: '#4caf50' }}>已完成</span>
            </Badge>
            <Badge count={stats.failed} size="small">
              <span style={{ fontSize: '12px', color: '#f44336' }}>失败</span>
            </Badge>
          </div>
        </div>
        <Space>
          <Button
            size="small"
            variant="text"
            icon={<SettingIcon />}
            onClick={() => {
              NotificationPlugin.info({
                title: '设置',
                content: '传输设置功能开发中...',
                duration: 2000,
              });
            }}
          />
          <Button
            size="small"
            variant="text"
            icon={<RemoveIcon />}
            onClick={() => setIsMinimized(true)}
            title="最小化"
          />
        </Space>
      </div>

      {/* 统计卡片 */}
      <div style={{ padding: '16px 20px', backgroundColor: '#f8f9fa' }}>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: '12px' }}>
          <div style={{ textAlign: 'center', padding: '12px', backgroundColor: '#e3f2fd', borderRadius: 6 }}>
            <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1976d2' }}>{stats.running}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>活跃传输</div>
          </div>
          <div style={{ textAlign: 'center', padding: '12px', backgroundColor: '#e8f5e8', borderRadius: 6 }}>
            <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#4caf50' }}>{stats.completed}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>已完成</div>
          </div>
          <div style={{ textAlign: 'center', padding: '12px', backgroundColor: '#ffebee', borderRadius: 6 }}>
            <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#f44336' }}>{stats.failed}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>失败</div>
          </div>
          <div style={{ textAlign: 'center', padding: '12px', backgroundColor: '#f5f5f5', borderRadius: 6 }}>
            <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#666' }}>{stats.total}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>总任务数</div>
          </div>
        </div>
      </div>

      {/* 操作按钮 */}
      <div style={{ padding: '12px 20px', borderBottom: '1px solid #e7e7e7' }}>
        <Space>
          <Button size="small" icon={<PoweroffIcon />} onClick={onPauseAll}>
            暂停全部
          </Button>
          <Button size="small" icon={<PlayCircleIcon />} onClick={onResumeAll}>
            恢复全部
          </Button>
          <Button size="small" icon={<RefreshIcon />} onClick={onRetryFailed}>
            重试失败
          </Button>
          <Button size="small" theme="danger" icon={<DeleteIcon />} onClick={onClearCompleted}>
            清除完成
          </Button>
        </Space>
      </div>

      {/* 标签页和任务列表 */}
      <div style={{ flex: 1, overflow: 'hidden' }}>
        <Tabs
          value={activeTab}
          onChange={(value) => setActiveTab(value as string)}
          style={{ height: '100%' }}
        >
          <Tabs.TabPanel value="all" label={`全部任务 ${stats.total}`} style={{ height: '100%', overflow: 'auto', padding: '0 20px' }}>
            {renderTaskList(filteredTasks)}
          </Tabs.TabPanel>
          <Tabs.TabPanel value="upload" label="上传中" style={{ height: '100%', overflow: 'auto', padding: '0 20px' }}>
            {renderTaskList(filteredTasks)}
          </Tabs.TabPanel>
          <Tabs.TabPanel value="download" label="下载中" style={{ height: '100%', overflow: 'auto', padding: '0 20px' }}>
            {renderTaskList(filteredTasks)}
          </Tabs.TabPanel>
          <Tabs.TabPanel value="completed" label={`已完成 ${stats.completed}`} style={{ height: '100%', overflow: 'auto', padding: '0 20px' }}>
            {renderTaskList(filteredTasks)}
          </Tabs.TabPanel>
          <Tabs.TabPanel value="failed" label={`失败 ${stats.failed}`} style={{ height: '100%', overflow: 'auto', padding: '0 20px' }}>
            {renderTaskList(filteredTasks)}
          </Tabs.TabPanel>
        </Tabs>
      </div>

      {/* 底部状态栏 */}
      <div style={{ padding: '8px 20px', borderTop: '1px solid #e7e7e7', fontSize: '12px', color: '#666', backgroundColor: '#fafafa' }}>
        并发上传: {stats.runningUploads} / {maxConcurrentUploads} | 活跃传输: {stats.running} | 完成: {stats.completed}
      </div>
    </div>
  );
};

export default TransferManager;
