import React from 'react';
import { Image } from 'tdesign-react';
import { getObjectUrlByFid } from '../../utils/blobImage';
import { MemoryOptimizer, NetworkOptimizer } from '../../utils/performanceOptimizer';
import LazyImage from '../OptimizedImage/LazyImage';

interface FidImageProps {
  fidOrUrl?: string;
  width?: number | string;
  height?: number | string;
  style?: React.CSSProperties;
  lazy?: boolean; // 是否启用懒加载
}

const isHttpOrAbs = (v: string) => /^https?:\/\//i.test(v) || v.startsWith('/');

const FidImage: React.FC<FidImageProps> = ({
  fidOrUrl = '',
  width = 60,
  height = 45,
  style,
  lazy = true // 默认启用懒加载
}) => {
  const [src, setSrc] = React.useState<string>('');

  React.useEffect(() => {
    let mounted = true;

    const loadImage = async () => {
      if (!fidOrUrl) {
        setSrc('');
        return;
      }

      if (isHttpOrAbs(fidOrUrl)) {
        setSrc(fidOrUrl);
        return;
      }

      // 检查缓存
      const cacheKey = `fid_${fidOrUrl}`;
      const cachedUrl = MemoryOptimizer.getCache(cacheKey);
      if (cachedUrl) {
        if (mounted) setSrc(cachedUrl);
        return;
      }

      try {
        // 使用请求去重避免重复获取同一个 FID
        const objectUrl = await NetworkOptimizer.deduplicateRequest(
          cacheKey,
          () => getObjectUrlByFid(fidOrUrl)
        );

        if (mounted) {
          setSrc(objectUrl);
          // 缓存结果
          MemoryOptimizer.setCache(cacheKey, objectUrl);
        }
      } catch (e) {
        // eslint-disable-next-line no-console
        console.error('FidImage getObjectUrlByFid error:', e);
        if (mounted) setSrc('');
      }
    };

    loadImage();
    return () => { mounted = false; };
  }, [fidOrUrl]);

  // 如果启用懒加载，使用优化的 LazyImage 组件
  if (lazy) {
    return (
      <LazyImage
        src={src || ''}
        defaultImage="/images/default-thumbnail.svg" // 默认缩略图
        alt="缩略图"
        width={typeof width === 'number' ? width : undefined}
        height={typeof height === 'number' ? height : undefined}
        style={{ borderRadius: 4, ...(style || {}) }}
        rootMargin="100px 0px" // 提前100px开始加载，提升用户体验
        threshold={0.01}       // 1%可见时触发

      />
    );
  }

  // 否则使用原有的 TDesign Image 组件
  return (
    <Image
      src={src || '/images/default-thumbnail.svg'}
      style={{ width, height, borderRadius: 4, ...(style || {}) }}
      fit="cover"
      error={<div style={{ width, height, display: 'flex', alignItems: 'center', justifyContent: 'center', backgroundColor: '#f5f5f5', borderRadius: 4, fontSize: 12, color: '#999' }}>无图片</div>}
    />
  );
};

export default FidImage;

