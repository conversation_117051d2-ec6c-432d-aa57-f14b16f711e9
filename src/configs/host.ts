export default {
  mock: {
    // 本地mock数据
    API: '',
  },
  development: {
    // 开发环境接口请求 - 连接本地后端
    API: 'http://9.208.245.89:3002/api',
  },
  docker: {
    // Docker容器化环境 - 通过Nginx代理
    API: '/api',
  },
  test: {
    // 测试环境接口地址
    API: 'https://previzcms.test.woa.com/api/test/v1',
  },
  release: {
    // 正式环境接口地址
    API: 'https://previzcms.test.woa.com/api/test/v1',
  },
  site: {
    // TDesign部署特殊需要 与release功能一致
    API: 'https://previzcms.test.woa.com/api/test/v1',
  },
};
